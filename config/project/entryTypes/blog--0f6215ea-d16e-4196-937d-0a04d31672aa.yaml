color: null
fieldLayouts:
  b968f8a2-6cfb-4ecd-ac33-b7944c98a8af:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-07T12:45:38+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 3ce7ec89-5559-4cc1-94cd-4dc29d4c50ed # Keyvisual title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b2b66caf-67df-4b80-b4c9-69d7a06c00d0
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T12:45:38+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 01d0e8fa-146c-4ced-a949-38bc9c37f4ae # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c183657c-8505-4b97-bdf6-6cfd0b9d9188
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T12:45:38+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1e63055a-995c-4414-9ce9-0bb8a4c2398a
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:42:30+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
                  layoutElementUid: 1e63055a-995c-4414-9ce9-0bb8a4c2398a
                  operator: notempty
                  uid: 6d0a8000-d8ab-4b54-a3fc-7f302fed608c
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 5720c7eb-5a37-428d-bd9a-7a90410a75cf # Generic text field
            handle: splineId
            includeInCards: false
            instructions: 'Add a spline model to show the model on top of the keyvisual. An ID should look something like this: P7ZG7I9D4cUO-Mop'
            label: 'Spline model ID'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 26a79057-b9d9-47b6-9d9d-6d5f9fd1d658
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T12:45:38+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 8e8b3faa-a3b2-4235-bc51-c22de4bd0eaf # Keyvisual show overlay
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 80e1afef-d848-4ebc-930c-9e8298e02181
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T12:45:38+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 0f812928-e150-4475-9976-5f7342f8a8d8 # Publication date
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2da7f1e8-170d-4f90-b330-2f87d2f6b8e9
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T12:45:38+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: e2821c62-718f-48bc-a360-ef173f449f4e # Blog labels
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: febf98f5-8ca1-4f99-a79b-89872b02ef03
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-07T12:45:38+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 8fcc86b8-ef77-4770-90f8-62e3d79a2693 # Related blog label
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3220eba5-b1d7-49d2-9311-d56c64b2f0f1
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-07T12:47:51+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: e2aae430-8aef-4ee2-82a3-e636e91bfacd # Auteur
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e5424dbc-3981-4671-a143-c149760838fb
            userCondition: null
            warning: null
            width: 100
        name: Keyvisual
        uid: 327751a4-5da1-42e0-87e0-c7bfa17a35fe
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-07T12:45:38+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 30dbca28-bd0a-4006-ae88-997b4b5d0c05
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 3707c322-c39a-4b41-98c0-c2778eb96c1e
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-01-07T12:45:38+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 39a845e8-3a6f-4a3b-b3c5-17de3c729618
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T12:45:38+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 09e0b955-52b6-4dd0-8a98-23e16f1c03c4 # SEO
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1171e33b-4306-4823-bea8-eb734a2e0a97
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: 95a74854-d62e-4eb9-85b4-646632f6065d
        userCondition: null
handle: blog
hasTitleField: true
icon: null
name: Blog
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
