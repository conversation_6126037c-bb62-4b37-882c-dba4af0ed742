color: null
fieldLayouts:
  5560deb1-e0c9-4cc0-b2cf-8490894ecf34:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-08-28T11:37:33+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: e933af0b-9b18-4d46-bd65-e1d88d8c7b3e
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: e7d63ef0-2e58-40f7-9c3a-0e83ecce5020 # Partner
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e3f7f1e7-18e3-4149-937e-c87b14cb6025
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 93f190db-1350-4c6a-962c-18254b83165a
        userCondition: null
handle: casePartner
hasTitleField: false
icon: handshake
name: 'Case Partner'
showSlugField: false
showStatusField: false
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
