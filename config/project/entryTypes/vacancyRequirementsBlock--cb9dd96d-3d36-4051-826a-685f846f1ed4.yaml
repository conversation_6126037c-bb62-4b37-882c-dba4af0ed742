color: null
fieldLayouts:
  af61b4a1-28dc-44a9-8ddb-69042c0902ef:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 9ea8f67f-6379-4e23-9495-b39a4fc2ab89 # Vacancy requirements - Requirement
            handle: null
            includeInCards: true
            instructions: null
            label: Requirement
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8886db24-8066-4480-92a7-e0c6000b8823
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 90a070f5-4793-4d45-acd8-827403e955a7
        userCondition: null
handle: vacancyRequirementsBlock
hasTitleField: false
icon: null
name: 'Vacancy requirements Block'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
