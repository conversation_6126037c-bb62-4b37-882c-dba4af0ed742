color: null
fieldLayouts:
  150c5d2b-5e92-4f9b-8d0f-77fde2180e7d:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 12e9dbee-4191-4592-9e2e-fbcd4fa319b7 # Footer call us - Phone url
            handle: null
            includeInCards: true
            instructions: null
            label: 'Phone url'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bba5edec-09d1-44ec-b0d3-9b6b606e87ad
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 47b41160-2172-4a62-91fa-bcfe15ecb7f2 # Footer call us - Phone label
            handle: null
            includeInCards: false
            instructions: null
            label: 'Phone label'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f50e403c-25ea-4474-a6ca-fd519301afb4
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 003b9434-1cfe-439a-af94-719c8625c14a
        userCondition: null
handle: footerCallUsBlock
hasTitleField: false
icon: null
name: 'Footer call us Block'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
