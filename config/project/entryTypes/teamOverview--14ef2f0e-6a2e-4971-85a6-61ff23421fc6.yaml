color: null
fieldLayouts:
  12c58f40-4eda-48cc-a59f-baecaa075405:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-01-30T09:26:57+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 78c49e01-0dfc-4b6c-8d11-537c13c24448
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:26:57+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bcabc845-7875-47df-96fb-73a819a9561e
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: dcb61668-5b66-4e96-959f-dc6cecb5464e
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-30T09:26:57+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 3ce7ec89-5559-4cc1-94cd-4dc29d4c50ed # Keyvisual title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 03ec0bcf-6b2f-479f-b9fb-ab5b3356dd5d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:26:57+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 01d0e8fa-146c-4ced-a949-38bc9c37f4ae # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3f5f6937-5f3b-4076-bb1c-852694a549b9
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:26:57+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 00d521e5-dd35-4be0-94f7-8904e6eab51d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:31:28+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
                  layoutElementUid: 00d521e5-dd35-4be0-94f7-8904e6eab51d
                  operator: notempty
                  uid: f2205d61-1908-4142-ab6a-2b7c196ddf84
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 5720c7eb-5a37-428d-bd9a-7a90410a75cf # Generic text field
            handle: splineId
            includeInCards: false
            instructions: 'Add a spline model to show the model on top of the keyvisual. An ID should look something like this: P7ZG7I9D4cUO-Mop'
            label: 'Spline model ID'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 32db501d-1bc9-4eab-9790-92702acfd04b
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:26:57+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 8e8b3faa-a3b2-4235-bc51-c22de4bd0eaf # Keyvisual show overlay
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a51127d9-0fc3-4734-bc2e-9fd1c6b3b096
            userCondition: null
            warning: null
            width: 100
        name: Keyvisual
        uid: a2171c08-a59b-43e7-b162-f55398fd2f69
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-30T09:26:57+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 09e0b955-52b6-4dd0-8a98-23e16f1c03c4 # SEO
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ff8deedf-bc44-4b54-b6ed-7e54d063f983
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: de3381c0-ffed-4bf4-bb37-4b89af7dd6bc
        userCondition: null
handle: teamOverview
hasTitleField: true
icon: null
name: 'Team Overview'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{section.name|raw}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
