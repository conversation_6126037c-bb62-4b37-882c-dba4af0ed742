color: null
fieldLayouts:
  6ca6e039-589e-469c-a943-c9dfb21da4a3:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-11-20T15:31:42+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 88f9a518-8307-4bfe-8300-3028c873a0e1
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-11-20T15:31:42+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 587049d8-dca9-4d14-8dae-be84a0d11e82 # Slider - kvSlider - Title
            handle: null
            includeInCards: true
            instructions: null
            label: Title
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 34031eb7-d6d0-4388-b7ec-270ab6b84763
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-11-20T15:31:42+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d1c3e964-b35c-4fb5-836f-8f0039b73015 # Slider - kvSlider - CTA
            handle: null
            includeInCards: false
            instructions: null
            label: CTA
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0a64835b-5817-4d34-9fb3-f6ac613c6657
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-11-20T15:31:42+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 8c9f17d3-6e3f-475b-b361-06504dab1c0b # Slider - kvSlider - Mobile image
            handle: null
            includeInCards: false
            instructions: null
            label: 'Mobile image'
            providesThumbs: true
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bc9c72c1-a5df-4040-9c11-362819067fe1
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-11-20T15:31:42+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 313d3a89-f0c4-4219-970e-e5c58b5c3a92 # Slider - kvSlider - Dekstop image
            handle: null
            includeInCards: false
            instructions: null
            label: 'Dekstop image'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1b8c64bd-b80b-4d0b-a188-add2d9c1b17a
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-11-20T15:31:42+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: f509b14b-9f99-4736-83ce-062f496c4928 # Slider - kvSlider - Video url
            handle: null
            includeInCards: false
            instructions: null
            label: 'Video url'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7eeaed49-b169-4df4-bd91-a7bdc3d05a9d
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 5e3fe185-01ad-43e2-b98a-9d3609a5bd2e
        userCondition: null
handle: slide
hasTitleField: false
icon: null
name: kvSlider
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
