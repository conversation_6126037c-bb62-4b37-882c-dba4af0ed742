color: null
fieldLayouts:
  f0cade7d-f7de-4608-a620-e3256c22751b:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: null
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 5e25a9e7-9981-4ebe-9631-32ad1a91b241
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: a6a8e5b9-124e-47c3-8610-a67f23013e4c
        userCondition: null
handle: caseLabel
hasTitleField: true
icon: null
name: 'Case label'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
