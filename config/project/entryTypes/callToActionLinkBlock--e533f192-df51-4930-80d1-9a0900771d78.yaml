color: null
fieldLayouts:
  a5329424-169b-4e92-a604-4ca17bc6965a:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: f5805ef3-c3ba-49af-85ab-5c56a50d4516 # Call to action link - Link
            handle: linkNode
            includeInCards: false
            instructions: null
            label: Link
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4486791d-b8dd-4abf-b350-3d0245e860ef
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 38d117c6-60f3-4d97-81e6-e274dab627af # Call to action link - Hover text
            handle: hoverText
            includeInCards: true
            instructions: null
            label: 'Hover text'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8411788f-87ad-41ac-acdf-9f74d3a0cd5f
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 46c549fd-43c0-4b1d-a8e2-8c489b0026ef
        userCondition: null
handle: callToActionLinkBlock
hasTitleField: false
icon: null
name: 'Call to action link Block'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
