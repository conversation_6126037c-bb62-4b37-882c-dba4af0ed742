color: null
fieldLayouts:
  a8313294-d40e-45f1-a679-0bcf73f94cdf:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: b8140e95-5084-4745-886d-752e620aabe5 # Keyvisual - Highlighted words
            handle: null
            includeInCards: true
            instructions: null
            label: 'Highlighted words'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c329bce0-2b24-4342-9cd1-5469ef6a1371
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 17a0a445-5134-478a-a801-c09d057e82f5 # Keyvisual - Keyvisual title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Keyvisual title'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b2a5eeab-d376-4210-a4c0-13a7e092b41c
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 98e45ea7-5bca-4158-a523-0bc821f023bc # Keyvisual - Slider delay
            handle: null
            includeInCards: false
            instructions: null
            label: 'Slider delay'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 15b86bdb-e3f0-41c7-b1be-a98287eefd65
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 4a7f8043-e6bd-42d5-949f-4cfaf9f8b75b # Keyvisual - Slider
            handle: null
            includeInCards: false
            instructions: null
            label: Slider
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2647aa04-230a-4ab3-90ac-5810ccd660b8
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 2ff9b758-b26b-45d0-84dd-46d8d71fdd5e
        userCondition: null
handle: keyVisualBlock
hasTitleField: false
icon: null
name: 'Keyvisual Block'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
