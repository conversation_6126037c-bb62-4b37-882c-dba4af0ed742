color: null
fieldLayouts:
  97bce54e-940c-47c7-a36d-e97ff00fe7bd:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-01-30T09:34:29+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 03524639-b8ec-4009-a5c2-82de9fe637d3
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:34:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 82ec93af-9033-4567-b8ef-d48cd9372ef9 # Logo slider
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1c76d163-0bac-484b-bd43-0d102b444f90
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:34:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: eb689e86-4ae8-4c98-9813-1973bc20725a
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 8c534dc9-c3fc-460a-a6f4-8172c0dc6221
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-30T09:34:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 3ce7ec89-5559-4cc1-94cd-4dc29d4c50ed # Keyvisual title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: afc4c874-3ed3-4922-a1ff-f92f0b157810
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:34:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 01d0e8fa-146c-4ced-a949-38bc9c37f4ae # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2f53822b-0819-4bbb-846c-331ea49cecb2
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:34:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: df907d67-e549-4fef-842b-4677dbc4d6d3
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:35:56+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
                  layoutElementUid: df907d67-e549-4fef-842b-4677dbc4d6d3
                  operator: notempty
                  uid: 2d533799-52cf-499a-b976-4f23930f1533
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 5720c7eb-5a37-428d-bd9a-7a90410a75cf # Generic text field
            handle: splineId
            includeInCards: false
            instructions: 'Add a spline model to show the model on top of the keyvisual. An ID should look something like this: P7ZG7I9D4cUO-Mop'
            label: 'Spline model ID'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5d84dd2f-a5da-484a-a369-0fcb764dff90
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:34:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 8e8b3faa-a3b2-4235-bc51-c22de4bd0eaf # Keyvisual show overlay
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: aa3a2246-0c48-4a16-8506-51841150f2e5
            userCondition: null
            warning: null
            width: 100
        name: Keyvisual
        uid: c12593ab-e7ac-41de-bd78-1d7b131ce55e
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-30T09:34:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 09e0b955-52b6-4dd0-8a98-23e16f1c03c4 # SEO
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7ce8e678-ba2c-491a-aee6-bf13b6f86903
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: 2c9f9dca-10e3-42f1-b078-d930f4ffeef1
        userCondition: null
handle: casesOverview
hasTitleField: true
icon: null
name: 'Cases Overview'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{section.name|raw}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
