color: null
fieldLayouts:
  de1efd04-c297-43ad-b111-018e5cc1f489:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: bd5f655f-f3fb-438d-8206-33e607e27ae9 # This is us - Title and image - Title
            handle: null
            includeInCards: true
            instructions: null
            label: Title
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0fcc5d73-abd2-4b42-b191-70e43d918d7b
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: cc7bbf93-6233-474d-a550-0352b8237506 # This is us - Title and image - Image
            handle: null
            includeInCards: false
            instructions: null
            label: Image
            providesThumbs: true
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4f04cf8b-a97e-41a7-a244-ece021f96f45
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 0e680405-07bf-49e6-b922-ed79a1c54d72 # This is us - Title and image - Image position
            handle: null
            includeInCards: false
            instructions: null
            label: 'Image position'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d13114b4-7816-4d1d-a91b-2c7ed35420d2
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: e52c0a8c-3b0e-459a-b862-12d792d4fa60
        userCondition: null
handle: titleImage
hasTitleField: false
icon: null
name: 'Title and image'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
