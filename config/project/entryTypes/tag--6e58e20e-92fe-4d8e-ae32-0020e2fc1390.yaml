color: null
fieldLayouts:
  fa31f51f-ad2c-4e96-9b4b-7a4016ba566d:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: null
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: fbe86f89-f566-48c7-9ff7-722be71896ba
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 252d2c70-f165-4997-ab3e-89659938a5ce # Items - Tag - Label
            handle: null
            includeInCards: true
            instructions: null
            label: Label
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b2f31fae-6552-4822-9bc0-7e978a896acf
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 9e2e6041-f9ac-403f-8e92-1dbc7d5e6572
        userCondition: null
handle: tag
hasTitleField: false
icon: text
name: Tag
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
