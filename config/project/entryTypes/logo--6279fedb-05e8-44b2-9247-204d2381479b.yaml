color: null
fieldLayouts:
  b3e48322-68e7-465e-9c21-7233ff68d266:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: null
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 9c9c9ddc-3376-4b69-b24b-23836dd52ff7
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 829c034c-aebe-43f4-8577-4b5a856906c6 # Items - Logo - Logo
            handle: null
            includeInCards: true
            instructions: null
            label: Logo
            providesThumbs: true
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b9753229-c696-41fa-a47b-8c9d4372338c
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 181f4b31-769d-4a90-af36-1018aedced08
        userCondition: null
handle: logo
hasTitleField: false
icon: image
name: Logo
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
