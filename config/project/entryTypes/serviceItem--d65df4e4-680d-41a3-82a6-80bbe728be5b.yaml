color: null
fieldLayouts:
  5bfeea0d-9a18-4fde-b904-96aa43e7a4d5:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: e07d63a2-4e0a-4837-b422-d1c26d4d9b60 # Items - Service item - Service title
            handle: null
            includeInCards: true
            instructions: null
            label: 'Service title'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2aa2fcfd-36dc-4e60-a6d3-10f40651591f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 9cead7d7-5212-48b4-8139-fa35bdf92487 # Items - Service item - Service description
            handle: null
            includeInCards: false
            instructions: null
            label: 'Service description'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 47676e10-2d75-47df-a5be-d6c2a0d869e1
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: c3c45d27-d1aa-4a3f-90b7-57e24d00bdf4 # Items - Service item - Service icon
            handle: null
            includeInCards: false
            instructions: null
            label: 'Service icon'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c9fc7cdc-c2a3-4ce6-ba2e-39d36d3ff9ae
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 35ddf4a6-d1e7-442f-9a37-e2998d287451
        userCondition: null
handle: serviceItem
hasTitleField: false
icon: null
name: 'Service item'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
