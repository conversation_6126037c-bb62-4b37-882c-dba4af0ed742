color: null
fieldLayouts:
  6cb51f0d-1491-4039-b2f7-1720ff46c2ff:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-12T08:43:20+00:00'
            elementCondition: null
            heading: Testimonial
            type: craft\fieldlayoutelements\Heading
            uid: 82580ad1-3a84-42f7-a607-92ae5c08af7f
            userCondition: null
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-09-12T08:38:21+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: d06277d2-0ac2-49e9-ad62-30e0d390c287
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-12T09:53:58+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 01d0e8fa-146c-4ced-a949-38bc9c37f4ae # Subtitle
            handle: heading
            includeInCards: false
            instructions: ' '
            label: Title
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e44e555a-a295-4f46-a086-5781511e704d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-12T08:43:20+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: abf7d301-fb1c-417d-a36e-f8160647f658 # Text
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4decf272-c935-4e60-9216-b35628a3b1a1
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-12T08:43:20+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: c992a6c5-31c2-48f9-9c71-4149ef842233 # Case/Partner
            handle: null
            includeInCards: false
            instructions: null
            label: 'Related case/partner'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0d9878bb-36c1-4f5d-abc4-20b3880e5259
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-12T12:07:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 4a6ba438-b609-4b51-a4b1-15bf4cded568 # Image
            handle: null
            includeInCards: false
            instructions: 'If not selected will use the selected case keyvisual image'
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2e2dfd56-9adf-4ad1-8300-ec892eb72752
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-12T08:43:20+00:00'
            elementCondition: null
            heading: Author
            type: craft\fieldlayoutelements\Heading
            uid: 99c8e5b7-767a-4d3f-801c-842d0faba229
            userCondition: null
          -
            dateAdded: '2024-09-12T08:43:20+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 4a6ba438-b609-4b51-a4b1-15bf4cded568 # Image
            handle: authorImage
            includeInCards: false
            instructions: null
            label: Headshot
            providesThumbs: true
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: afbd65cc-7ef8-42d6-b905-82db72a6c566
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-12T08:43:20+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d8f42343-9e51-4569-8257-0bc5783d8305 # Heading
            handle: authorName
            includeInCards: true
            instructions: null
            label: Name
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2aba1539-1f68-46b0-86ea-5fc72c1b6272
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-12T08:43:20+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d8f42343-9e51-4569-8257-0bc5783d8305 # Heading
            handle: authorJobTitle
            includeInCards: false
            instructions: null
            label: 'Job title'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6ce75057-0cd4-4187-9d9b-1cac4a5cf190
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: a4cdd29d-71ba-475b-990b-d2dea4c931aa
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-12T15:11:26+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 191c3a1e-3e7c-4b79-8a15-807d12c17298 # Toggle
            handle: imageLeft
            includeInCards: false
            instructions: null
            label: 'Image left'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2972610a-ac28-4da2-b71d-f0dfcdf6c888
            userCondition: null
            warning: null
            width: 100
        name: Settings
        uid: ef9aedb0-123b-45f4-b2c5-65a03fbd9dd2
        userCondition: null
handle: testimonial
hasTitleField: false
icon: award
name: Testimonial
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{caseOrPartner.one.title} - {heading}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
