color: null
fieldLayouts:
  99ff4841-810a-461f-9172-ca76a0753072:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: null
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 4ae47259-c50f-4f69-8c34-3f4721df9e2f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 0443bfa2-8bae-4948-9519-665a050cf78f # Logo slider - Items
            handle: null
            includeInCards: false
            instructions: null
            label: Items
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5405f6dc-7c7f-4bc6-87a5-3959b6d6ad40
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 8774e060-4088-4052-a023-ea3fc6f8b29b
        userCondition: null
handle: logoSliderBlock
hasTitleField: false
icon: null
name: 'Logo slider Block'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
