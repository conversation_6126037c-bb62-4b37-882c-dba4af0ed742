color: null
fieldLayouts:
  6bfbb046-4323-4d1d-b691-b125aecdb3d9:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-09-25T10:25:04+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: c1934038-8bcf-4608-afae-f2392fb6741b
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-25T10:25:04+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 1bd49f15-5fbf-40e9-95e3-385122074ab0 # Show pop-up
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 44a80f21-a161-4aee-8456-30a9b540a91c
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-25T10:25:04+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: 1bd49f15-5fbf-40e9-95e3-385122074ab0 # Show pop-up
                  uid: 801884ba-0bb9-4e14-8cfb-5e3a8612566f
                  value: true
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: fda30e8d-8d2b-475a-b382-229c71f3bfc7 # Pop-up
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3c3811ee-ac8e-49d5-8464-d50e4e7f8282
            userCondition: null
            warning: null
            width: 100
        name: Pop-up
        uid: 1bd2fbff-ffc9-4342-9c71-9bc2281f5bfd
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-25T10:26:45+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 1bd49f15-5fbf-40e9-95e3-385122074ab0 # Show pop-up
            handle: showVirtualHuman
            includeInCards: false
            instructions: null
            label: 'Show virtual human'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ec69f5dd-5b1c-4b35-ae2c-6b1a149eb453
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-25T10:41:18+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d8f42343-9e51-4569-8257-0bc5783d8305 # Heading
            handle: virtualHumanButtonTitle
            includeInCards: false
            instructions: null
            label: 'Button title'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6458e43f-e8bd-436c-b7a4-a70fdb40940f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-25T14:29:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d8f42343-9e51-4569-8257-0bc5783d8305 # Heading
            handle: virtualHumanPopupTitle
            includeInCards: false
            instructions: 'Title to show in the popup'
            label: Title
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 01125f98-fa3a-49df-ad7c-da19559f702f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-25T14:29:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 6850c800-68a8-41f1-99d2-61be158f7096 # Case description
            handle: virtualHumanDescription
            includeInCards: false
            instructions: 'Description to show in the popup'
            label: Description
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d6d4a546-e177-4cc0-9920-478ae99b569e
            userCondition: null
            warning: null
            width: 100
        name: 'Virtual human'
        uid: d04c3e47-0ce9-4c4f-9401-89969991f57b
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-27T08:09:25+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 4d0ce0c8-698d-4442-b418-9eb2a64b3b97 # Company address
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: dbd1f342-18d0-450d-9615-36dc7581e6e6
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-27T08:26:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d1b3bc0a-9b34-409f-966c-2a57604bf509 # Company region
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7cae95c7-af70-4e87-b4a4-1e2c0647fa9d
            userCondition: null
            warning: null
            width: 50
        name: 'Company details'
        uid: 7c1916b0-9da1-4d91-bfbc-29c9db3d4793
        userCondition: null
handle: general
hasTitleField: false
icon: null
name: 'General settings'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{section.name|raw}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
