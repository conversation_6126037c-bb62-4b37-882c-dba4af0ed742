color: null
fieldLayouts:
  747af04c-9fd9-4e15-b9a2-8d1dfdc30b91:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 1e18491e-190d-45fa-aee8-7c679e41948d # Intro Text - Intro
            handle: null
            includeInCards: true
            instructions: null
            label: Intro
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ea500c94-955e-4a8b-9ad5-b2a12a4a1f76
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: c469a399-07d6-44b9-96fe-6a13e7890b39 # Intro Text - Highlighted words
            handle: highlightedWords
            includeInCards: false
            instructions: null
            label: 'Highlighted words'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bc572fdc-dbf1-414e-a90c-920c6a998e73
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 741e5804-eff1-456d-89e1-c0aa4955bdbc
        userCondition: null
handle: introBlock
hasTitleField: false
icon: null
name: 'Intro Text Block'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
