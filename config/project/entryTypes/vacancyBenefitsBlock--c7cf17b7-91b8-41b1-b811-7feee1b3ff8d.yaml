color: null
fieldLayouts:
  9a60dea6-c1ca-4c83-a2df-eab17c96ec4b:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: aca8a580-a7a3-4202-a540-dad3a0361755 # Vacancy benefits - Icon
            handle: null
            includeInCards: true
            instructions: null
            label: Icon
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d4d0d9e2-0696-4f61-bcb3-c8adbcb75a9d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 047b3ba5-c922-43b0-8542-4341e2fe9bee # Vacancy benefits - Label
            handle: label
            includeInCards: false
            instructions: null
            label: Label
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a84e9883-5832-4b35-aca9-4abcec00b3c5
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 9b6968ab-2f5b-4561-85aa-abb1c4b30408 # Vacancy benefits - Details
            handle: null
            includeInCards: false
            instructions: null
            label: Details
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 63926b5e-8aca-4b05-b960-ad5762318f8b
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: c0043a42-2e90-408e-961a-4a16963d5dfe
        userCondition: null
handle: vacancyBenefitsBlock
hasTitleField: false
icon: null
name: 'Vacancy benefits Block'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
