color: null
fieldLayouts:
  574b0fa3-1530-4898-acde-75eed3622a21:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 1189fbd0-e46b-4556-905c-fcca40eb1e8e # Services - Service item - Service image
            handle: null
            includeInCards: false
            instructions: null
            label: 'Service image'
            providesThumbs: true
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2cd00542-00c8-4395-bb8c-aceb55859e9d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: a58bc66d-025f-4ee2-9eda-597dcc6c7be2 # Services - Service item - Service title
            handle: serviceTitle
            includeInCards: true
            instructions: null
            label: 'Service title'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 437cdae6-cd38-4c41-9185-f7c41643fafd
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 734d805d-c361-4b7a-a6dd-3bb1654bbbf9 # Services - Service item - Service description
            handle: serviceDescription
            includeInCards: false
            instructions: null
            label: 'Service description'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4ae6cdd4-4db3-43df-921f-07e4d60ee7f2
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: null
            editCondition: null
            elementCondition: null
            fieldUid: 250a7dc4-4f23-4ee9-bd73-6df496704c85 # Services - Service item - Service link
            handle: null
            includeInCards: false
            instructions: null
            label: 'Service link'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 751ac99a-b121-4c9d-96d5-372907f79e6e
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: c959498e-4fef-4eab-8136-b634fd702a16
        userCondition: null
handle: serviceItem2
hasTitleField: false
icon: null
name: 'Service item 2'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
