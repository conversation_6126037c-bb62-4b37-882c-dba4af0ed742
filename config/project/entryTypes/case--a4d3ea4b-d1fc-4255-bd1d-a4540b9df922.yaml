color: null
fieldLayouts:
  13b40f8a-2d0b-47e8-91e5-e081479546f3:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            heading: Keyvisual
            type: craft\fieldlayoutelements\Heading
            uid: 44b1f08e-97c6-429a-a335-5f1ea3dbdbe6
            userCondition: null
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 3ce7ec89-5559-4cc1-94cd-4dc29d4c50ed # Keyvisual title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c653a68d-3552-4755-a835-4827b19fdd30
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 01d0e8fa-146c-4ced-a949-38bc9c37f4ae # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7a08130b-6847-4b82-9e2a-9680d8893599
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b14dc9eb-**************-e20e255e7a61
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:41:56+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
                  layoutElementUid: b14dc9eb-**************-e20e255e7a61
                  operator: notempty
                  uid: 1493edb4-1d1c-4e5e-8a94-ca5612a71135
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 5720c7eb-5a37-428d-bd9a-7a90410a75cf # Generic text field
            handle: splineId
            includeInCards: false
            instructions: 'Add a spline model to show the model on top of the keyvisual. An ID should look something like this: P7ZG7I9D4cUO-Mop'
            label: 'Spline model ID'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9ae83809-72fb-4c08-b69f-31b025582b72
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 8e8b3faa-a3b2-4235-bc51-c22de4bd0eaf # Keyvisual show overlay
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0229f939-d443-483e-9fe2-4d87561e0c5b
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            heading: 'Keyvisual bar'
            type: craft\fieldlayoutelements\Heading
            uid: 7ee0b3a9-3502-4a81-b1b3-d2fec609966f
            userCondition: null
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 7593357f-0024-45f1-9b07-30ce83f008d7
            userCondition: null
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 2198f51d-9b08-49b2-bba1-8683008d9072 # Case labels
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3d0bc7a4-6b11-4286-90cb-e9518fc84f4c
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 87015ea0-4510-4561-9def-92de1e813f4b # Awards and Partners
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6d3f36b6-8f93-4003-b340-f488cfa9414d
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 7713a404-999e-49de-b289-e2e23218241e # Branches
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7c0009a0-d1bd-437e-a3df-6af1a6bdd495
            userCondition: null
            warning: null
            width: 100
        name: Keyvisual
        uid: b8d23350-e698-4354-acc0-bebd0f802296
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            heading: 'Default content block'
            type: craft\fieldlayoutelements\Heading
            uid: 4ea610a9-f261-4d38-8b56-e943975039dc
            userCondition: null
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 1f679bb8-7dfe-4c7d-99d8-4aeab8992724 # Case heading
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 80a76eba-d91d-4d7c-ab83-b8a711a213c2
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: a8ceac97-bb91-4df8-a085-092ca706f54f # Results heading
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5c1c7564-e2b1-4a0b-9b6b-a27cc5f321eb
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 6850c800-68a8-41f1-99d2-61be158f7096 # Case description
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 56c3ad6d-9c94-443b-8340-************
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 52c4aab8-1e0a-4222-a4bd-106d280eaadb # Results
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 638c33cd-6ca7-4fcb-96a9-464d040451aa
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            heading: 'Variable content blocks'
            type: craft\fieldlayoutelements\Heading
            uid: 3eeac376-24b0-482c-bab3-41342af90234
            userCondition: null
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 7a684b99-9cd6-44e3-996c-ee3bde4cd705
            userCondition: null
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bd2e022f-b0fb-431c-84c9-67d1eb583445
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 69d68358-c031-4a98-953d-2fc785e1c156
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-08-28T11:37:33+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 6ddef98b-88b2-4b29-9cd6-cf25c0cad7ff
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 09e0b955-52b6-4dd0-8a98-23e16f1c03c4 # SEO
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 055baf48-8d22-4655-8f3f-924ba36abb07
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: e627cfa4-55ab-40aa-b1c3-f4905e99d21a
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: a1920b37-a9af-4b81-b980-dd3869f6c9dd # Case type
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 644e0432-f979-4120-8592-ce571ec37c46
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            heading: Footer
            type: craft\fieldlayoutelements\Heading
            uid: 345fd1d7-546b-4961-bd5b-52974bbe6576
            userCondition: null
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 59af6831-e62b-41ef-ad8e-3900d239f122
            userCondition: null
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            dismissible: false
            elementCondition: null
            style: tip
            tip: 'This page uses a custom footer that will display the next case. You cannot set custom content for the footer on case detail pages.'
            type: craft\fieldlayoutelements\Tip
            uid: 7a0fc051-3f28-422c-92f2-7cbc1d17550b
            userCondition: null
        name: Settings
        uid: 5ab479b0-252d-4da7-aa45-fbf1a0ce9496
        userCondition: null
handle: case
hasTitleField: true
icon: null
name: Case
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
