color: null
fieldLayouts:
  1eabfb8a-f0b4-4c60-9a61-e7aafd06b50f:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 4591ff8a-d908-40b6-a73c-e54b9d7c3157 # Team member
            handle: null
            includeInCards: false
            instructions: 'Select the team member who is the primary contact for this vacancy'
            label: 'Vacancy contact'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0ebfa2c3-e1f9-47e5-9821-eb03fae048fb
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 3402f2a7-cd29-414c-b0de-43b6e34b9e5f # Vacancy link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 49360855-01e0-4364-ad75-19e8f392475a
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            elementCondition: null
            heading: Keyvisual
            type: craft\fieldlayoutelements\Heading
            uid: 2fc18440-d61d-4179-b2a4-2b13fadb5213
            userCondition: null
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: dacae371-f770-4652-9a02-d5e36427abf6 # Job title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e1b2292b-c3d3-441a-9b97-211374f5bbe4
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 01d0e8fa-146c-4ced-a949-38bc9c37f4ae # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 86579495-d8be-4b86-98ba-631c7187e0da
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: fffb215e-223d-400d-916d-e4afa647dc40
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 2b1e32e9-2e3e-40fd-a0e4-f7ed8b976d24
            userCondition: null
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            elementCondition: null
            heading: Content
            type: craft\fieldlayoutelements\Heading
            uid: d55949b8-4ff1-4190-8595-82367552dbe4
            userCondition: null
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 4b6d31d9-ab4b-4e97-a056-2bbfb8efa684 # Vacancy tags
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8d388627-7311-4610-a23e-8a9751dc5a63
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 1c635f3a-727b-4864-8b0c-7bdaa799648e # Vacancy content
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 453242e0-6792-4b2f-b1d8-b85f17cb2dda
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            elementCondition: null
            heading: Requirements
            type: craft\fieldlayoutelements\Heading
            uid: 2a5743d4-c4e7-44aa-a1bc-5ab904c5474d
            userCondition: null
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: bf90faf1-becb-468e-8992-cb679d6bdc99 # Vacancy requirements
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c1446ac4-a105-4ec4-a887-8ee04963316f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: dc44344a-72a8-4d32-b34f-e96749028cb8 # Vacancy secondary link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bd0eeb3e-d785-40b7-aef9-bf064509257e
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: fbf4cd2d-078e-4a42-95df-460eab9edf53 # Custom modal content
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 93ce69d4-3fb4-45ae-8c6f-23d42300415d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: fbf4cd2d-078e-4a42-95df-460eab9edf53 # Custom modal content
                  uid: 39f8a0f1-9b56-4749-b84c-0648738f830b
                  value: true
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: b1fce06f-a605-4d4d-8d0c-5252490a7d2b # Vacancy modals content
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 083397b7-f237-4fb5-82ee-e06ef9652564
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            elementCondition: null
            heading: Benefits
            type: craft\fieldlayoutelements\Heading
            uid: bb04a6fd-db0f-495e-932d-88069cab4deb
            userCondition: null
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 3f730a00-4b7f-492e-9c27-310ba10d3e8a # Vacancy benefits
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2699bf71-43e6-4111-9aae-2576e3a3dac9
            userCondition: null
            warning: null
            width: 100
        name: 'Vacancy details'
        uid: 4f7e032a-862e-49bc-a3e2-27489a6c152c
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6bcca884-4d51-4662-87cc-edefc52ca246
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 94dd1c4e-0b8d-4522-9b37-505dd0220785
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-09-26T12:58:35+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 4d10ee97-8d14-421e-ac27-e792ba263b73
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 09e0b955-52b6-4dd0-8a98-23e16f1c03c4 # SEO
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d01773ea-df4d-4b3d-83b3-76f2364362fd
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T12:58:35+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 27ab67af-0c49-4e99-bac1-13a9ec5c78eb # Vacancy text
            handle: null
            includeInCards: false
            instructions: null
            label: 'Structured data description'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b463830f-dda5-4391-9ecb-3344d61d4c86
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T13:19:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d6d5891e-cfff-450a-ac2a-94a4e1395de4 # Vacancy Employment Type
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ee3a13b8-eca0-4a7e-9020-dd85318816f6
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-26T13:19:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 98f809d2-95d4-46c1-89d0-8ab36ca0c175 # Vacancy Base Salary
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: dbab1d7e-02e7-45cd-9e22-79fbad9d62ab
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-26T13:19:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: eff8f77a-631a-4e53-b397-4502ff2567f3 # Vacancy Job Benefits
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c08f0859-8acb-4a37-abc3-88a3f2749a13
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T13:19:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: ad360542-fb98-4efb-901d-8fd696c996b2 # Vacancy Responsibilities
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6713044e-e12c-4f2f-9fe3-1cd41d4fdec5
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T13:19:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: ae61069d-dc9b-4b96-aefb-330b2a9cf3c4 # Vacancy Qualifications
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d1599627-e5cf-4153-b492-b0ebfee5be67
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-26T13:19:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 38c74cac-c3ec-4c1d-9909-4cac726cb594 # Vacancy Industry
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 767cb757-3ca2-42f1-a417-b6507e44e37b
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-26T13:19:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: cd21c9da-13cb-49ea-b662-4f457ed58d51 # Vacancy Work Hours
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 20497b20-6bfc-483d-9a3e-5805911bf469
            userCondition: null
            warning: null
            width: 50
        name: SEO
        uid: 99bca7a0-a9e0-43cb-a10c-b5750d06a249
        userCondition: null
handle: vacancy
hasTitleField: true
icon: null
name: Vacancy
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
