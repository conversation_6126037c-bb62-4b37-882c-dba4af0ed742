color: null
fieldLayouts:
  1610db1c-3739-4887-9d26-63ea83b6966a:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: cc2b1f33-a2c6-4e75-923e-f059c479c8cd # Has page
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0c811d72-4c9e-432a-b8cc-41ad247d0d29
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: bcb01ace-dba6-40f2-86d4-b1d67a9d48b5 # Image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: fbebe255-4246-4388-86a2-813a78cc4a88
            userCondition: null
            warning: null
            width: 100
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-01-07T13:26:13+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: Name
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 12a091c8-9361-40eb-aad4-38bdf632600a
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: dacae371-f770-4652-9a02-d5e36427abf6 # Job title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 78c3cf64-56f0-492f-862e-4dacd5f64582
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 690acb2d-c87f-44da-97ba-860324b25280 # E-mail
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c08b4d30-8cd1-4e3f-9a18-90e124fabf99
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: fad26ab4-0611-4970-b9f6-40d15e146e21 # Phone number
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 442b2487-f1ef-4b49-9b3c-e6df183d76c9
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: e24238a5-679a-4302-b94d-bc3a4d19afeb # Whatsapp
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6579a248-e5dd-4a44-8e8a-1127c818bc99
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-07T13:29:46+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 0531e7c1-df4f-4c83-98dc-a042485945a5 # Footer socials - Instagram
            handle: null
            includeInCards: false
            instructions: null
            label: Instagram
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 37089350-a354-4a6d-9279-bd5acfd8aff8
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:29:46+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 9311ee02-7e48-4bf8-b82e-0d80f147dd6a # x
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d029119c-6bc0-40a8-815e-7f882f602670
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 59fd61f6-60f1-4d16-bc8e-982df5cb0936 # Linkedin
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9f22e329-7cbd-4d4d-ac48-7b951b4f5161
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T10:39:52+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 1ebfab92-6975-4849-ba57-142a3bc8f201 # Custom social links
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: da342116-5e19-4bbe-8d48-67d2783d7627
            userCondition: null
            warning: null
            width: 100
        name: Profile
        uid: 3cf68bd6-2dc9-4659-b7e1-992cd82bfb9c
        userCondition: null
      -
        elementCondition:
          class: craft\elements\conditions\entries\EntryCondition
          conditionRules:
            -
              class: craft\fields\conditions\LightswitchFieldConditionRule
              fieldUid: cc2b1f33-a2c6-4e75-923e-f059c479c8cd # Has page
              uid: 26146013-2a8d-421a-8b6e-bf53087b9002
              value: true
          elementType: craft\elements\Entry
          fieldContext: global
        elements:
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 8e17ea5e-9ed9-437b-880c-4b70d87aef9a # Keyvisual type
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e66fece1-93c7-4948-9569-1f1de85495af
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: d6e024fd-**************-fca5c18fd0bc
            userCondition: null
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 3ce7ec89-5559-4cc1-94cd-4dc29d4c50ed # Keyvisual title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 18cc9f05-f8b3-4b45-b6d9-a676c13ddb6c
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 8e17ea5e-9ed9-437b-880c-4b70d87aef9a # Keyvisual type
                  layoutElementUid: e66fece1-93c7-4948-9569-1f1de85495af
                  operator: in
                  uid: cafd7070-3bc7-4b0f-8a46-83fe22092414
                  values:
                    - default
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 01d0e8fa-146c-4ced-a949-38bc9c37f4ae # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f68e4f6c-1620-4be8-bbad-403d4e37a4fb
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 8e17ea5e-9ed9-437b-880c-4b70d87aef9a # Keyvisual type
                  layoutElementUid: e66fece1-93c7-4948-9569-1f1de85495af
                  operator: in
                  uid: 064ee633-a66d-4d7c-b0d1-ee5508c96c6d
                  values:
                    - default
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7705adb4-a7da-4117-9930-41bf0f1d5baf
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T09:27:43+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 8e17ea5e-9ed9-437b-880c-4b70d87aef9a # Keyvisual type
                  layoutElementUid: e66fece1-93c7-4948-9569-1f1de85495af
                  operator: in
                  uid: 51b6581b-e80b-4324-9104-0618aa73f8de
                  values:
                    - default
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 78cde064-b9f3-460e-a53e-a48a635890ea # Background image
                  layoutElementUid: 7705adb4-a7da-4117-9930-41bf0f1d5baf
                  operator: notempty
                  uid: 2a0bde2a-ab4b-4ffd-b961-3ec0544fc03c
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 5720c7eb-5a37-428d-bd9a-7a90410a75cf # Generic text field
            handle: splineId
            includeInCards: false
            instructions: 'Add a spline model to show the model on top of the keyvisual. An ID should look something like this: P7ZG7I9D4cUO-Mop'
            label: 'Spline model ID'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3c262056-82c5-4edb-bf02-7b0e277836ae
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 8e17ea5e-9ed9-437b-880c-4b70d87aef9a # Keyvisual type
                  layoutElementUid: e66fece1-93c7-4948-9569-1f1de85495af
                  operator: in
                  uid: 07de1a8f-335c-47cd-a73c-edd08bc9cbb5
                  values:
                    - default
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 8e8b3faa-a3b2-4235-bc51-c22de4bd0eaf # Keyvisual show overlay
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 18d50469-536e-45fd-85e0-c6e420009a26
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 8e17ea5e-9ed9-437b-880c-4b70d87aef9a # Keyvisual type
                  layoutElementUid: e66fece1-93c7-4948-9569-1f1de85495af
                  operator: in
                  uid: 27bb1b7b-124d-49e3-b531-b98b9dfd51b9
                  values:
                    - clean
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: abf7d301-fb1c-417d-a36e-f8160647f658 # Text
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c1d35a35-173a-4905-bbf5-0c7e83ac5ad8
            userCondition: null
            warning: null
            width: 100
        name: Keyvisual
        uid: 26aee0e0-d924-4a20-bde1-cf7baf329730
        userCondition: null
      -
        elementCondition:
          class: craft\elements\conditions\entries\EntryCondition
          conditionRules:
            -
              class: craft\fields\conditions\LightswitchFieldConditionRule
              fieldUid: cc2b1f33-a2c6-4e75-923e-f059c479c8cd # Has page
              uid: e94b707f-1e5c-4153-9a29-c69fadea7730
              value: true
          elementType: craft\elements\Entry
          fieldContext: global
        elements:
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bf883ead-dbc1-4e63-a185-d34c863fb1e5
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 4c186e53-0dac-4f9c-8e12-1845d2aea7d4
        userCondition: null
      -
        elementCondition:
          class: craft\elements\conditions\entries\EntryCondition
          conditionRules:
            -
              class: craft\fields\conditions\LightswitchFieldConditionRule
              fieldUid: cc2b1f33-a2c6-4e75-923e-f059c479c8cd # Has page
              uid: 132b8e39-54ee-4f8a-a04a-4c5e081ff578
              value: true
          elementType: craft\elements\Entry
          fieldContext: global
        elements:
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 09e0b955-52b6-4dd0-8a98-23e16f1c03c4 # SEO
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b50ed7ac-b215-4046-abc4-f778b363484e
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: ef42dc1f-4200-4721-874e-884d37edcfc5
        userCondition: null
      -
        elementCondition:
          class: craft\elements\conditions\entries\EntryCondition
          conditionRules:
            -
              class: craft\fields\conditions\LightswitchFieldConditionRule
              fieldUid: cc2b1f33-a2c6-4e75-923e-f059c479c8cd # Has page
              uid: d8764ffa-0aee-4c65-b072-fb7b9c9d657e
              value: true
          elementType: craft\elements\Entry
          fieldContext: global
        elements:
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 76daec75-83e9-4e89-9ba5-dddc303cf28e
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            dismissible: false
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: 23eebcf8-222f-474f-a8c3-ffa7c02d6e7e
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            style: tip
            tip: 'Optionally overwrite the contact section on this page'
            type: craft\fieldlayoutelements\Tip
            uid: 9398dc64-6640-42a0-9f8c-f312bd2a9c07
            userCondition: null
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: 8f3aeb7d-7753-4abf-b842-05cd1e925f4b
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: ba2a0559-d95f-4a52-8742-43c64bbf064f # Footer title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 79b90466-d1e1-415e-bfd1-be8e0f8752d5
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: 18bb4625-b995-4619-a7fa-823d19fc6463
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 99c56249-6483-4719-90d8-de7b89f8c0d2 # Footer contact
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 94f55033-ac7b-4692-bbc9-ae36b3a7613d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: 3a3414d8-1836-4316-aa06-6e475591aa98
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: f023b5a8-2699-469c-b0a8-c5f4f83d8c87 # Slider text
            handle: null
            includeInCards: false
            instructions: null
            label: 'Footer slider text'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d7a044cf-341a-4deb-b2f7-f8e084388891
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T13:26:13+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: 56cf9c11-7da0-459b-a487-3b1f3e938893
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e5d60224-e78d-46ac-9db3-c989e24b6e06 # Footer slider link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8176dbaa-80b2-42e8-8a52-be058c307faa
            userCondition: null
            warning: null
            width: 100
        name: Footer
        uid: f5f38e6c-3ef0-4e3b-85d5-712f16ab22f3
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-08T09:00:04+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: bc2ea128-3b72-473c-aa04-e4009f275253 # Toon blog artikelen
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 20bfeb30-f979-45e3-b63d-3d86174b5dee
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-08T09:00:04+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: c161e1ce-fb8b-411c-9ab4-b8e281e68ab7 # Uitgelichte blog artikelen
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bdd97ed1-4e8a-466d-9415-6625a0b44094
            userCondition: null
            warning: null
            width: 100
        name: Blogs
        uid: 19e8143a-c5d4-4165-95be-2012f7abf1b9
        userCondition: null
handle: teamMember
hasTitleField: true
icon: null
name: 'Team member'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: none
