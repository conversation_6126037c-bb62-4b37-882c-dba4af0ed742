color: null
fieldLayouts:
  e3f2e7fc-1f80-4f9d-84c6-faee5bf50dea:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 02b5fa2d-14be-4f81-bce8-2346967c4476 # Keyvisual
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: cb0ada76-1487-4212-ac5e-710586646a78
            userCondition: null
            warning: null
            width: 100
        name: Keyvisual
        uid: 03b4b607-82e7-4de7-8384-9f4d766ff50d
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: fa3f2c29-7ee1-4e1c-998b-30626f8ba861
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: d1e9c5a8-73d0-4d66-aa7f-59526a2533e8
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-03-17T15:33:06+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 003e2809-e0ad-41e8-b7f0-773ba7344670
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 09e0b955-52b6-4dd0-8a98-23e16f1c03c4 # SEO
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6cb2fcd3-0a13-4db4-acea-c91d7467b610
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: a6f83e26-575a-4967-a6c6-11463f85633f
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c99f1665-f668-4214-8d33-cdabdb5d96ce
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            dismissible: false
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: aef0e395-083e-4d13-b44c-4f4470edafaf
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            style: tip
            tip: 'Optionally overwrite the contact section on this page'
            type: craft\fieldlayoutelements\Tip
            uid: c3b4e9b6-0ae1-4605-b166-065980b110cc
            userCondition: null
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: a3af2b83-e64b-4f58-9543-75e864b80f3f
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: ba2a0559-d95f-4a52-8742-43c64bbf064f # Footer title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 26f45c68-5ef8-4cc3-b2a3-686f35c3a9ca
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: 224a0b9b-b85f-4403-9df5-41c1d319ede4
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 99c56249-6483-4719-90d8-de7b89f8c0d2 # Footer contact
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a4a09986-8f1b-428d-bb07-f97bfd261539
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: 430bb8ab-e71c-482a-b637-7842e334b6ab
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: f023b5a8-2699-469c-b0a8-c5f4f83d8c87 # Slider text
            handle: null
            includeInCards: false
            instructions: null
            label: 'Footer slider text'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: da1045ec-6de5-4c3f-91bb-5f9d9730c176
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-03-17T15:33:06+00:00'
            editCondition: null
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: b2735c83-3520-4bf2-ba76-a8b7f6abcb81 # Hide footer contact
                  uid: b48da1c1-6ebc-478d-8cc6-adb91d93f18e
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e5d60224-e78d-46ac-9db3-c989e24b6e06 # Footer slider link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 41ba28ab-8f2d-45ff-b68a-31caae39f997
            userCondition: null
            warning: null
            width: 100
        name: Footer
        uid: 690cc45e-45e3-4fc1-824f-3183b9c79e02
        userCondition: null
handle: home
hasTitleField: false
icon: null
name: Home
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{section.name|raw}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
