defaultPlacement: end
fieldLayouts:
  13d7e064-fc95-49b1-bc6c-1cc3bf147ed3:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-08-28T11:37:33+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\TitleField
            uid: b9ea0f1d-b69f-4c7d-a5bd-1ba34c226e82
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 01d0e8fa-146c-4ced-a949-38bc9c37f4ae # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 48746727-1b32-4f74-b45a-9a64e7896354
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: verbb\navigation\fieldlayoutelements\NodeTypeElements
            uid: 821c1ec5-5428-4bd5-b1ac-665592cc4c2c
            userCondition: null
            warning: null
          -
            attribute: newWindow
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            id: null
            includeInCards: false
            instructions: null
            label: null
            mandatory: false
            orientation: null
            providesThumbs: false
            requirable: true
            required: false
            tip: null
            translatable: false
            type: verbb\navigation\fieldlayoutelements\NewWindowField
            uid: da25c589-0654-4937-8f0e-8e51c60a3978
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: ad4b3fa4-ccb1-4dc3-ae40-a9bb579099c3
            userCondition: null
          -
            dateAdded: '2024-08-28T11:37:33+00:00'
            editCondition: null
            elementCondition:
              class: verbb\navigation\elements\conditions\NodeCondition
              conditionRules:
                -
                  class: craft\elements\conditions\LevelConditionRule
                  maxValue: ''
                  operator: '='
                  step: 1
                  uid: 9a9d2192-718d-4dd2-81f0-72334a5f3d64
                  value: '1'
              elementType: verbb\navigation\elements\Node
              fieldContext: global
            fieldUid: 191c3a1e-3e7c-4b79-8a15-807d12c17298 # Toggle
            handle: simpleSubMenu
            includeInCards: false
            instructions: null
            label: 'Simple sub menu'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 49abdf89-70a9-4122-b66a-dc5ec9e92d93
            userCondition: null
            warning: null
            width: 100
        name: Node
        uid: 147d8999-e17c-4a01-9017-4f8b4a8130d1
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            attribute: urlSuffix
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            autofocus: false
            class: null
            dateAdded: '2024-08-28T11:37:33+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            mandatory: false
            max: null
            maxlength: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: true
            required: false
            size: null
            step: null
            tip: null
            title: null
            translatable: false
            type: verbb\navigation\fieldlayoutelements\UrlSuffixField
            uid: 7bece5b9-a02a-4d14-9101-bda460289f85
            userCondition: null
            warning: null
            width: 100
          -
            attribute: classes
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            autofocus: false
            class: null
            dateAdded: '2024-08-28T11:37:33+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            mandatory: false
            max: null
            maxlength: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: true
            required: false
            size: null
            step: null
            tip: null
            title: null
            translatable: false
            type: verbb\navigation\fieldlayoutelements\ClassesField
            uid: 61502c24-d624-4d60-87a1-d51f9cb7c323
            userCondition: null
            warning: null
            width: 100
          -
            attribute: customAttributes
            dateAdded: '2024-08-28T11:37:33+00:00'
            elementCondition: null
            id: null
            includeInCards: false
            instructions: null
            label: null
            mandatory: false
            orientation: null
            providesThumbs: false
            requirable: true
            required: false
            tip: null
            translatable: false
            type: verbb\navigation\fieldlayoutelements\CustomAttributesField
            uid: 624e6161-cf22-4151-becf-96e2f12647f3
            userCondition: null
            warning: null
            width: 100
        name: Advanced
        uid: eb6aac82-aa23-4946-8f39-30a4c16c2a9c
        userCondition: null
handle: mainNavigation
instructions: null
maxNodes: null
name: 'Main navigation'
permissions:
  craft\elements\Asset:
    enabled: '1'
    permissions: '*'
  craft\elements\Category:
    enabled: '1'
    permissions: '*'
  craft\elements\Entry:
    enabled: '1'
    permissions: '*'
  craft\elements\Tag:
    enabled: ''
    permissions: '*'
  verbb\navigation\nodetypes\CustomType:
    enabled: '1'
  verbb\navigation\nodetypes\PassiveType:
    enabled: '1'
  verbb\navigation\nodetypes\SiteType:
    enabled: '1'
  verbb\supertable\elements\SuperTableBlockElement:
    enabled: ''
    permissions: '*'
propagationMethod: none
siteSettings:
  4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: # Redkiwi Dutch
    enabled: true
  ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: # Redkiwi English
    enabled: true
sortOrder: 3
structure:
  maxLevels: null
  uid: 32883ef6-40ee-4c54-954c-93fe6cc321a7
