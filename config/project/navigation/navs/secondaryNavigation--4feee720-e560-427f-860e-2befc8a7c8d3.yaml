defaultPlacement: end
fieldLayouts:
  96d257d4-73dc-4575-a1ca-1df26e4136aa:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-12-24T08:32:35+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\TitleField
            uid: e7656347-30d4-455b-ac78-9151ad48bb7f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-24T08:32:35+00:00'
            elementCondition: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: verbb\navigation\fieldlayoutelements\NodeTypeElements
            uid: 5d56c5f5-e0c8-42a9-ad02-5ad4774512f3
            userCondition: null
            warning: null
          -
            attribute: newWindow
            dateAdded: '2024-12-24T08:32:35+00:00'
            elementCondition: null
            id: null
            includeInCards: false
            instructions: null
            label: null
            mandatory: false
            orientation: null
            providesThumbs: false
            requirable: true
            required: false
            tip: null
            translatable: false
            type: verbb\navigation\fieldlayoutelements\NewWindowField
            uid: 68b02725-21b9-4f74-b905-1821edc50174
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-24T08:35:29+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 6769aadb-d4b8-4aa8-b11e-812e8b7c3b78
            userCondition: null
          -
            dateAdded: '2024-12-24T08:35:29+00:00'
            editCondition: null
            elementCondition:
              class: verbb\navigation\elements\conditions\NodeCondition
              conditionRules:
                -
                  class: craft\elements\conditions\LevelConditionRule
                  maxValue: ''
                  operator: '='
                  step: 1
                  uid: 8c29ca6c-e65e-4229-a985-eaf62e3f202b
                  value: '1'
              elementType: verbb\navigation\elements\Node
              fieldContext: global
            fieldUid: 191c3a1e-3e7c-4b79-8a15-807d12c17298 # Toggle
            handle: simpleSubMenu
            includeInCards: false
            instructions: null
            label: 'Simple sub menu'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9ab9113d-79a3-43ca-a76e-298eb2f07d8d
            userCondition: null
            warning: null
            width: 100
        name: Node
        uid: e35efc63-37d8-4c6f-ac1e-e9ce7e52dcf3
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            attribute: urlSuffix
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            autofocus: false
            class: null
            dateAdded: '2024-12-24T08:32:35+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            mandatory: false
            max: null
            maxlength: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: true
            required: false
            size: null
            step: null
            tip: null
            title: null
            translatable: false
            type: verbb\navigation\fieldlayoutelements\UrlSuffixField
            uid: fef900a3-ea8a-4106-867d-c805239aeca1
            userCondition: null
            warning: null
            width: 100
          -
            attribute: classes
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            autofocus: false
            class: null
            dateAdded: '2024-12-24T08:32:35+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            mandatory: false
            max: null
            maxlength: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: true
            required: false
            size: null
            step: null
            tip: null
            title: null
            translatable: false
            type: verbb\navigation\fieldlayoutelements\ClassesField
            uid: 459d9dc5-4e56-45ed-9125-a5423854db7c
            userCondition: null
            warning: null
            width: 100
          -
            attribute: customAttributes
            dateAdded: '2024-12-24T08:32:35+00:00'
            elementCondition: null
            id: null
            includeInCards: false
            instructions: null
            label: null
            mandatory: false
            orientation: null
            providesThumbs: false
            requirable: true
            required: false
            tip: null
            translatable: false
            type: verbb\navigation\fieldlayoutelements\CustomAttributesField
            uid: 7efba8c1-cf75-48aa-a3da-a97c387596d2
            userCondition: null
            warning: null
            width: 100
        name: Advanced
        uid: 2e7c919e-fc2e-4ef7-9b30-1aa420f57460
        userCondition: null
handle: secondaryNavigation
instructions: null
maxNodes: null
name: 'Secondary navigation'
permissions:
  craft\elements\Asset:
    enabled: '1'
    permissions: '*'
  craft\elements\Category:
    enabled: '1'
    permissions: '*'
  craft\elements\Entry:
    enabled: '1'
    permissions: '*'
  craft\elements\Tag:
    enabled: ''
    permissions: '*'
  verbb\navigation\nodetypes\CustomType:
    enabled: '1'
  verbb\navigation\nodetypes\PassiveType:
    enabled: '1'
  verbb\navigation\nodetypes\SiteType:
    enabled: '1'
propagationMethod: all
siteSettings:
  4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: # Redkiwi Dutch
    enabled: true
  ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: # Redkiwi English
    enabled: true
sortOrder: 5
structure:
  maxLevels: 2
  uid: a19276ee-08eb-4a54-a83c-cff81bdeb928
