childBlocks: null
color: null
conditions: null
description: ''
enabled: true
entryType: null
field: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
fieldLayouts:
  ffbe0222-eefc-4686-8e9a-e9a798cc663f:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-05-27T13:51:31+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: dcd27536-3587-49bb-a07e-f4e7babfb550 # Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 492aa66f-3418-4c05-85b4-846fac2150f6
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-27T13:51:31+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 01d0e8fa-146c-4ced-a949-38bc9c37f4ae # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2e0fc193-d9bd-4113-9557-c8f954048838
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-27T13:51:31+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: c6af9219-93a6-4e77-bf57-a16da9d1d6f9 # USP Image list
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d0485c5b-439b-4b2e-a8a6-dc9f19e292dc
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 3f7af941-a17b-4894-b5d1-d88c8c8969d6
        userCondition: null
group: 7dffce16-658a-48ab-b219-520b30d6e8f6 # Basic elements
groupChildBlockTypes: true
handle: uspImageList
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'USP image list'
topLevel: true
