childBlocks: null
color: null
conditions: null
description: ''
enabled: true
entryType: null
field: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
fieldLayouts:
  a1b2c3d4-e5f6-7890-abcd-ef1234567890:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-30T12:00:00+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: abf7d301-fb1c-417d-a36e-f8160647f658 # Text
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 11111111-1111-1111-1111-111111111111
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-30T12:00:00+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 9222dd52-5975-4cf9-b915-d16fe4a2234a # Video
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-2222-2222-************
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-30T12:00:00+00:00'
            editCondition: null
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\TextFieldConditionRule
                  fieldUid: 9222dd52-5975-4cf9-b915-d16fe4a2234a # Video
                  layoutElementUid: *************-2222-2222-************
                  operator: notempty
                  uid: *************-3333-3333-************
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: cdac2931-0ec0-40fc-ae2d-6fe345be5493 # Fallback image
            handle: null
            includeInCards: false
            instructions: 'Fallback image for the video (poster)'
            label: 'Fallback image'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-4444-4444-************
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T12:00:00+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 32455d9c-de7a-4457-9807-409fdeb87b9c # Link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-5555-5555-************
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: *************-6666-6666-************
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-30T12:00:00+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: a1b2c3d4-e5f6-7890-abcd-ef1234567890 # Text video layout
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-7777-7777-************
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-30T12:00:00+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 99f7e43b-135c-4f75-851b-4189085a0d3a # Margin bottom
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-8888-8888-************
            userCondition: null
            warning: null
            width: 100
        name: Settings
        uid: *************-9999-9999-************
        userCondition: null
group: 7dffce16-658a-48ab-b219-520b30d6e8f6 # Basic elements
groupChildBlockTypes: true
handle: textVideo
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'Text Video'
topLevel: true
