childBlocks: null
color: null
conditions: null
description: ''
enabled: true
entryType: null
field: 50c56008-d769-45d7-8b2f-53f1f54e6e4e # Content blocks
fieldLayouts:
  10b4127a-ef41-4d12-9aff-68256250773f:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-12T08:30:02+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: abf7d301-fb1c-417d-a36e-f8160647f658 # Text
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 08c4c813-4f65-46f7-be9a-8f8110ac7243
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-12T08:30:02+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 9222dd52-5975-4cf9-b915-d16fe4a2234a # Video
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 72e06c14-9c56-46f3-839e-622a9598fc34
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-30T11:26:41+00:00'
            editCondition: null
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 9222dd52-5975-4cf9-b915-d16fe4a2234a # Video
                  layoutElementUid: 72e06c14-9c56-46f3-839e-622a9598fc34
                  operator: notempty
                  uid: abc28624-cedd-461f-97db-8edc5c978eca
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: cdac2931-0ec0-40fc-ae2d-6fe345be5493 # Fallback image
            handle: null
            includeInCards: false
            instructions: 'Fallback image for the video (poster)'
            label: 'Fallback image'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d8ebbc80-5762-49e9-8eb1-6c66e59ce6fa
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-12T08:30:02+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 32455d9c-de7a-4457-9807-409fdeb87b9c # Link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c6ac4fef-d2fe-4625-8f6f-965dd18036ea
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 5d8c7bba-fe7e-492a-bf94-56027f88bc7d
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-12T08:30:02+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: a1b2c3d4-e5f6-4890-abcd-ef1234567890 # Text video layout
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 22520702-5f2c-425e-b4b0-db683b857b8d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-12T08:30:02+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 99f7e43b-135c-4f75-851b-4189085a0d3a # Margin bottom
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 77c97ccd-c547-4c4d-a228-3c9a07d4dbe0
            userCondition: null
            warning: null
            width: 100
        name: Settings
        uid: 35c5d1aa-e73c-460d-ba4a-b9de69b059ff
        userCondition: null
group: 7dffce16-658a-48ab-b219-520b30d6e8f6 # Basic elements
groupChildBlockTypes: true
handle: textVideo
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'Text Video'
topLevel: true
