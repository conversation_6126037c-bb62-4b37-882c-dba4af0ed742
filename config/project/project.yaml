dateModified: 1751009138
elementSources:
  craft\elements\Entry:
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: '*'
      tableAttributes:
        - section
        - postDate
        - expiryDate
        - link
      type: native
    -
      heading: Structures
      type: heading
    -
      defaultSort:
        - title
        - asc
      defaultViewMode: ''
      disabled: false
      key: singles
      tableAttributes:
        - uri
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:e9956651-6a06-4e5a-90b6-ecef2f35b81f' # Pages
      tableAttributes:
        - postDate
        - expiryDate
        - link
      type: native
    -
      key: 'section:49bed2a4-a5bd-4086-ab84-b923500cdd83' # Partners
      type: native
    -
      heading: Kennis
      type: heading
    -
      defaultSort:
        - structure
        - asc
      disabled: false
      key: 'section:ae1830de-aa26-4bd5-b724-0ffb4ec2b268' # Case detail page
      tableAttributes:
        - postDate
        - expiryDate
        - author
        - link
      type: native
    -
      defaultSort:
        - postDate
        - desc
      disabled: false
      key: 'section:d8ab3984-29dd-489c-bc57-b3aef2d29ec2' # Blog detail pages
      tableAttributes:
        - postDate
        - expiryDate
        - author
        - link
      type: native
    -
      defaultSort:
        - title
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:6ecbac93-4310-491f-8c08-4b1d7d991fd6' # Woordenboek woorden
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      heading: Labels
      type: heading
    -
      key: 'section:4d5bb44f-c8ce-4db6-a707-0a29bdab337d' # Case labels
      type: native
    -
      defaultSort:
        - structure
        - asc
      disabled: false
      key: 'section:c9648e23-ae35-462e-88f5-8a8776614293' # Blog labels
      tableAttributes:
        - postDate
        - expiryDate
        - author
        - link
      type: native
    -
      key: 'section:7d1684ab-2bf8-45b6-af2e-f0129812472d' # Branch
      type: native
    -
      heading: Kanalen
      type: heading
    -
      key: 'section:54902690-4fb9-4a42-a112-26e6b8b1a6b7' # Team member
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:a1a77965-214c-4eee-b96b-376d23fd8b6b' # FAQ
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      key: 'section:32556adb-e3e5-43dc-8789-08df95ec1643' # Woordenboek categorieën
      type: native
    -
      heading: 'Site Settings'
      type: heading
    -
      condition:
        class: craft\elements\conditions\entries\EntryCondition
        conditionRules:
          -
            class: craft\elements\conditions\entries\SectionConditionRule
            operator: in
            uid: 5caf884d-c100-4b50-8fbb-dfa587039d6d
            values:
              - 78085d7f-d6cb-48d5-9db8-dae7b05363c2 # Footer
              - 61946b9d-5ab6-40da-8dc7-fcafbca2b556 # Navigation
              - abd71f45-de36-49fd-bf38-4abcc09ec5de # Four o four
        elementType: craft\elements\Entry
        fieldContext: global
      defaultSort:
        - title
        - asc
      key: 'custom:71387350-fa1c-4f46-bceb-0e7e625246a2'
      label: Globals
      tableAttributes:
        - section
        - uid
        - dateCreated
        - dateUpdated
      type: custom
email:
  fromEmail: <EMAIL>
  fromName: Redkiwi
  replyToEmail: null
  template: ''
  transportSettings:
    command: '/usr/sbin/sendmail -bs'
  transportType: craft\mail\transportadapters\Sendmail
fs:
  images:
    hasUrls: true
    name: Images
    settings:
      path: '@assetsPath/images'
    type: craft\fs\Local
    url: '@assetsUrl/images'
graphql:
  publicToken:
    enabled: false
    expiryDate: null
meta:
  __names__:
    0bff92c6-38fa-44f9-8c27-b98c90903380: 'Link Block' # Link Block
    0e1fe906-02d3-4a9b-8966-37b2f3adb1db: 'Cases Overview' # Cases Overview
    00e2a05b-8b24-4bc7-ac0e-967c116d5047: 'Images ' # Images 
    0e680405-07bf-49e6-b922-ed79a1c54d72: 'This is us - Title and image - Image position' # This is us - Title and image - Image position
    0f6215ea-d16e-4196-937d-0a04d31672aa: Blog # Blog
    0f812928-e150-4475-9976-5f7342f8a8d8: 'Publication date' # Publication date
    0fb3138d-c50a-4a26-a5d4-3835daeeaa7a: 'Contact details header' # Contact details header
    1bd49f15-5fbf-40e9-95e3-385122074ab0: 'Show pop-up' # Show pop-up
    1c4b6cf1-963b-4dcc-bbf7-3584a4bdc18e: 'General settings' # General settings
    1c101ac4-8a9a-4845-9b30-01fbedaff2ea: Contact # Contact
    1c635f3a-727b-4864-8b0c-7bdaa799648e: 'Vacancy content' # Vacancy content
    01d0e8fa-146c-4ced-a949-38bc9c37f4ae: Subtitle # Subtitle
    1d0e108a-cdfd-48b7-8b5c-0616400a3148: 'USP image' # USP image
    1e18491e-190d-45fa-aee8-7c679e41948d: 'Intro Text - Intro' # Intro Text - Intro
    1ebfab92-6975-4849-ba57-142a3bc8f201: 'Custom social links' # Custom social links
    1f562e1b-ebc9-40d0-80db-1d01a320cb49: 'Multiple links - Hover text' # Multiple links - Hover text
    1f679bb8-7dfe-4c7d-99d8-4aeab8992724: 'Case heading' # Case heading
    2b4d4b6a-121f-4358-bbac-379949f580c5: 'This is us' # This is us
    02b5fa2d-14be-4f81-bce8-2346967c4476: Keyvisual # Keyvisual
    3bb008de-edf3-40ba-ac6d-d0c68f1d73f1: 'Images - Image item - Text' # Images - Image item - Text
    3cc1e2fb-533f-4763-8419-7da5b9ce68a0: 'Contact Form' # Contact Form
    3cca4b45-62aa-4ca9-a728-cddca9e7b90e: Default # Default
    3ce7ec89-5559-4cc1-94cd-4dc29d4c50ed: 'Keyvisual title' # Keyvisual title
    3ce72123-b5aa-4b0b-a540-fe99d14e4696: 'Call to action image' # Call to action image
    3deb482a-c599-4b6c-b6e0-16306ed65570: 'Footer socials - Linkedin' # Footer socials - Linkedin
    3f730a00-4b7f-492e-9c27-310ba10d3e8a: 'Vacancy benefits' # Vacancy benefits
    3f1565cb-6350-46cc-ab11-84016abb873d: 'Whitepaper form' # Whitepaper form
    4a6ba438-b609-4b51-a4b1-15bf4cded568: Image # Image
    4a7f8043-e6bd-42d5-949f-4cfaf9f8b75b: 'Keyvisual - Slider' # Keyvisual - Slider
    4a89724f-6b07-412d-bc0d-bb7981a09173: 'Text Image Sticky' # Text Image Sticky
    4b6d31d9-ab4b-4e97-a056-2bbfb8efa684: 'Vacancy tags' # Vacancy tags
    4be78365-9c43-4e7b-95ae-aaed4d184e31: 'Title and image' # Title and image
    4c0ecdb1-1a03-459c-b7ca-49a8e62b5743: 'Partners overview' # Partners overview
    4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: 'Redkiwi Dutch' # Redkiwi Dutch
    4d0ce0c8-698d-4442-b418-9eb2a64b3b97: 'Company address' # Company address
    4d5bb44f-c8ce-4db6-a707-0a29bdab337d: 'Case labels' # Case labels
    4e019017-8071-4dcc-9c6e-2cca176bd3a9: 'Vacancy modals content - < 70% Gif' # Vacancy modals content - < 70% Gif
    4edf168b-d1e6-4f05-b288-61eba855998d: Navigation # Navigation
    4f4233d5-d84b-4ed8-b868-002076008b1a: 'Intro Text' # Intro Text
    4f5436df-91f5-45cf-9af2-bbac7bfda8b4: 'Results - Result - Heading' # Results - Result - Heading
    4feee720-e560-427f-860e-2befc8a7c8d3: 'Secondary navigation' # Secondary navigation
    5b582adf-58eb-4897-a325-1ba725a2033d: 'Budget options' # Budget options
    5b3615e4-49f2-4363-81be-91381257a43c: 'Contact page title' # Contact page title
    5c313780-e7b6-4f67-a537-033800530ea7: 'Quiz code' # Quiz code
    5cacd5f9-debe-4774-812d-4939d386df48: 'Results - Visit - Heading' # Results - Visit - Heading
    5dcfbb07-6d8c-46d0-b1ab-7ae10313f6d8: 'Dictionary Label' # Dictionary Label
    5def1990-695c-4988-970a-c13c48b6075c: 'Keyvisual Block' # Keyvisual Block
    5e69d69c-55c4-4fe9-a206-2c194d332f77: 'Multiple links - Style' # Multiple links - Style
    5ec03d65-e54f-411f-831d-2f0578fb70b4: 'Footer address' # Footer address
    6b6e65e3-ce3a-440f-9869-2ece0ac28e67: 'Footer links Block' # Footer links Block
    6be7bea0-e705-4d8d-8848-f6f9e5d589ac: FAQ’s # FAQ’s
    6bf5b599-4857-48e3-b986-c9c38e444ea5: 'Subscription form' # Subscription form
    6d9c9ac7-eff9-40ac-9ee1-cc216ce4a549: 'Service item 2' # Service item 2
    6da82470-2a28-4f3d-92de-f0df115de9f8: 'Links - Link - Link' # Links - Link - Link
    6e3b1069-3218-4df2-a7eb-2b02cb4c1b4f: Home # Home
    6e58e20e-92fe-4d8e-ae32-0020e2fc1390: Tag # Tag
    6ecbac93-4310-491f-8c08-4b1d7d991fd6: 'Woordenboek woorden' # Woordenboek woorden
    6edd6b38-b807-4b6b-9db9-1cf08307416a: 'Vacancy modals content - < 90% Title' # Vacancy modals content - < 90% Title
    06f73d6a-4f35-4a11-b3fa-180f754e9543: 'Pop-up Block' # Pop-up Block
    6f443007-2188-4501-99c4-8a121f3b2b79: 'Related blogs' # Related blogs
    06febb01-41c9-4e25-b1bd-5716fb98cda2: 'FAQ Questions' # FAQ Questions
    7b33819e-41a1-4f60-9ef2-cdda71391a7a: USPS # USPS
    7ba86b72-86fa-42fb-95e0-4264ab45bc6c: 'Pop-up - Short title' # Pop-up - Short title
    7bc5e22b-446c-4d61-a714-7a18c0ea9e4f: 'Multiple links' # Multiple links
    7ccbb09e-532d-4f3c-8266-8ef47b8b2d10: 'Vacancy modals content Block' # Vacancy modals content Block
    7d1684ab-2bf8-45b6-af2e-f0129812472d: Branch # Branch
    7dffce16-658a-48ab-b219-520b30d6e8f6: 'Basic elements' # Basic elements
    7e106a0a-9f05-4d0d-a287-abf32ddc7c43: 'Active campaign' # Active campaign
    8a7514c9-e57c-4d7d-b519-3ab2fa1b125d: 'Quotes - Quote item - Quotee' # Quotes - Quote item - Quotee
    8becce03-00a5-4ac0-9d2c-56bba4cb9b93: Editors # Editors
    8c9f17d3-6e3f-475b-b361-06504dab1c0b: 'Slider - kvSlider - Mobile image' # Slider - kvSlider - Mobile image
    8dcf96b2-9fc2-4be3-92c2-8c581f16c4d0: 'Blog label' # Blog label
    8e8b3faa-a3b2-4235-bc51-c22de4bd0eaf: 'Keyvisual show overlay' # Keyvisual show overlay
    8e17ea5e-9ed9-437b-880c-4b70d87aef9a: 'Keyvisual type' # Keyvisual type
    8fbe7144-9c5d-46a3-9c96-339546bf81a1: 'Pop-up - Text' # Pop-up - Text
    8fcc86b8-ef77-4770-90f8-62e3d79a2693: 'Related blog label' # Related blog label
    8ff9ff62-3bb1-4848-84ca-a44867afae9c: 'Case Partner' # Case Partner
    9b6968ab-2f5b-4561-85aa-abb1c4b30408: 'Vacancy benefits - Details' # Vacancy benefits - Details
    9cead7d7-5212-48b4-8139-fa35bdf92487: 'Items - Service item - Service description' # Items - Service item - Service description
    9d5a8d4f-4539-4a15-8745-7ef6c0b21d96: 'Cases slider' # Cases slider
    9dd0779d-d479-4e1b-b359-f8da12acd357: 'Contact details address' # Contact details address
    09e0b955-52b6-4dd0-8a98-23e16f1c03c4: SEO # SEO
    9e63dcb0-3f3e-4f89-813f-cc13e79bb435: Accountmanagers # Accountmanagers
    9ea8f67f-6379-4e23-9495-b39a4fc2ab89: 'Vacancy requirements - Requirement' # Vacancy requirements - Requirement
    12e9dbee-4191-4592-9e2e-fbcd4fa319b7: 'Footer call us - Phone url' # Footer call us - Phone url
    14ef2f0e-6a2e-4971-85a6-61ff23421fc6: 'Team Overview' # Team Overview
    15d4e76d-9d6f-4d46-8a0d-18fcbd177678: 'Columns images' # Columns images
    17a0a445-5134-478a-a801-c09d057e82f5: 'Keyvisual - Keyvisual title' # Keyvisual - Keyvisual title
    21b224b0-1d5d-4966-b4cc-3b7c88323c97: 'Four o four' # Four o four
    24a11cb5-34e3-41c8-9455-60fd0cb51b79: 'Simple 2' # Simple 2
    27ab67af-0c49-4e99-bac1-13a9ec5c78eb: 'Vacancy text' # Vacancy text
    27e084b0-e4be-4456-8caa-b80f3c2bdd58: 'Social Media Embed' # Social Media Embed
    31e62058-b612-4c0a-97dc-93e4fba63087: 'Quotes - Quote item - Quote' # Quotes - Quote item - Quote
    32bf94ee-0afc-4b91-8d8a-b7713d19238b: 'Show latest blogs' # Show latest blogs
    32ee1ae3-113b-47ae-bcbb-5f042cc399f2: Visit # Visit
    38c74cac-c3ec-4c1d-9909-4cac726cb594: 'Vacancy Industry' # Vacancy Industry
    38d117c6-60f3-4d97-81e6-e274dab627af: 'Call to action link - Hover text' # Call to action link - Hover text
    041ad40a-d447-44cc-b0ee-1e80c9780172: Footer # Footer
    43d9d14c-3c0a-4ac9-948a-1fb16537e73e: 'Services container' # Services container
    44fecd17-3b42-484e-8e9f-5b565ed681c6: 'Logo slider' # Logo slider
    45afd462-f73d-44ed-838a-385d9354d918: 'General settings' # General settings
    45da528d-1e7c-4814-a674-ccd20aee4ab2: 'Partners doorways' # Partners doorways
    047b3ba5-c922-43b0-8542-4341e2fe9bee: 'Vacancy benefits - Label' # Vacancy benefits - Label
    47b41160-2172-4a62-91fa-bcfe15ecb7f2: 'Footer call us - Phone label' # Footer call us - Phone label
    47c17095-209a-409b-9188-82733f386e75: Testimonial # Testimonial
    48ccf6bd-d207-425d-b369-e2ba537dd8b9: 'Vacancy modals content - Perfect match text' # Vacancy modals content - Perfect match text
    48fb9510-1283-4f2e-b700-96ea5cdc84e8: Branch # Branch
    49bed2a4-a5bd-4086-ab84-b923500cdd83: Partners # Partners
    50c56008-d769-45d7-8b2f-53f1f54e6e4e: 'Content blocks' # Content blocks
    52c4aab8-1e0a-4222-a4bd-106d280eaadb: Results # Results
    053c68b5-b5a4-4a3a-a580-23b981215682: 'Text left' # Text left
    54a13d54-96d7-491d-9130-f686e7c3339a: 'Footer call us' # Footer call us
    54e50544-5fbd-44d9-a073-5f0e0b405b0c: 'Contact question options' # Contact question options
    56fa5a6c-34e9-4b45-bcb5-b81e6e0bdbc8: 'Column Image' # Column Image
    57c77792-e0db-4e4f-896b-9226949a0fd2: 'Link - Link' # Link - Link
    58f2871c-10ab-4a39-92a6-69a161eee397: 'USP Image list item' # USP Image list item
    59fd61f6-60f1-4d16-bc8e-982df5cb0936: Linkedin # Linkedin
    62d0f6dc-d38a-47fd-9809-6a57975d389e: Visual # Visual
    63fa796f-8450-4f7f-8a97-e297f5b32e23: 'Logo slider Block' # Logo slider Block
    69a7187b-acfd-42c8-829e-7712e52d1e59: 'Footer socials Block' # Footer socials Block
    69c184df-a664-46f6-93b4-e1a296e8df16: 'Images - Image item - Title' # Images - Image item - Title
    71af82d1-e0c5-4b1f-8e3e-91910e4df7bc: Categorie # Categorie
    74eb6a2d-734b-47d4-b709-4008bfb3e263: 'Alt NL' # Alt NL
    75b8a27c-1c33-4442-b1a0-e6b03a6090e9: 'Newsletter/whitepaper element' # Newsletter/whitepaper element
    76a915d0-54dc-401e-8e60-493a03817a86: 'Title and text' # Title and text
    76bc9537-dea6-4061-ad14-54e458e0836d: 'Footer legal stuff' # Footer legal stuff
    76c49880-d312-45c2-ae6b-75a59aecde98: Redkiwi # Redkiwi
    77e8fe71-bfce-40a3-a8f6-8dcf4a12e5c3: 'Custom social link' # Custom social link
    78cde064-b9f3-460e-a53e-a48a635890ea: 'Background image' # Background image
    79b0ccd3-46df-4fd6-b6f7-cd3b3b0c61dc: Text # Text
    79fc8d28-9bf0-4bd6-af52-ab46aa0fa0d2: 'Contact details phone number' # Contact details phone number
    82c26617-054d-4f1b-bb25-fe25d1c472f6: Quote # Quote
    82cbc2d1-0aa8-41b4-9ac1-8859782884f4: Quotes # Quotes
    82ec93af-9033-4567-b8ef-d48cd9372ef9: 'Logo slider' # Logo slider
    84d66cc2-2a3c-4c43-874b-0b7fbd5d75c7: 'Results - Visit - Url' # Results - Visit - Url
    85c41ae2-7626-4112-a2b4-a3da2e02a42e: 'Footer links' # Footer links
    85fd75b0-4ee5-41df-81cd-8c704ba767ff: Subheading # Subheading
    87d8349a-aa1f-4921-8c92-870526e97e5f: 'Images grid' # Images grid
    88fca3e9-604b-40b8-b78e-d21e6f7e7bd9: Home # Home
    94f991ea-03e6-4517-a3e2-08abbe1cf997: 'USP icon' # USP icon
    94fd7e7b-b7b6-460a-9584-2409f77fb76c: 'Footer address - Url' # Footer address - Url
    95d26310-b74a-43be-97fa-9fbc927c907b: Simple # Simple
    98e45ea7-5bca-4158-a523-0bc821f023bc: 'Keyvisual - Slider delay' # Keyvisual - Slider delay
    98ef0f41-a2ac-4de0-87a1-d219ca602d9b: Video # Video
    98f809d2-95d4-46c1-89d0-8ab36ca0c175: 'Vacancy Base Salary' # Vacancy Base Salary
    99c56249-6483-4719-90d8-de7b89f8c0d2: 'Footer contact' # Footer contact
    99f7e43b-135c-4f75-851b-4189085a0d3a: 'Margin bottom' # Margin bottom
    186ef124-abe2-450f-a105-dec8d0996c12: 'USPS - USP' # USPS - USP
    191c3a1e-3e7c-4b79-8a15-807d12c17298: Toggle # Toggle
    250a7dc4-4f23-4ee9-bd73-6df496704c85: 'Services - Service item - Service link' # Services - Service item - Service link
    252d2c70-f165-4997-ab3e-89659938a5ce: 'Items - Tag - Label' # Items - Tag - Label
    268d229d-46b9-40ff-9141-437f8783de57: 'Pop-up - Button' # Pop-up - Button
    313d3a89-f0c4-4219-970e-e5c58b5c3a92: 'Slider - kvSlider - Dekstop image' # Slider - kvSlider - Dekstop image
    328a6afb-7761-49e3-abf8-f0ec43b283c9: Images # Images
    404f57cc-2947-4063-9983-e84ad374bd41: 'Team members' # Team members
    0443bfa2-8bae-4948-9519-665a050cf78f: 'Logo slider - Items' # Logo slider - Items
    467d1bf0-b42f-4e72-ab56-9acf045cc5ee: 'Image item' # Image item
    525a9860-9614-46db-aa8d-f2fd15aef3e8: 'Menus - Menu title' # Menus - Menu title
    530a6805-d95c-4103-bdd7-d81296cbd6d3: 'Two columns text' # Two columns text
    0531e7c1-df4f-4c83-98dc-a042485945a5: 'Footer socials - Instagram' # Footer socials - Instagram
    624f6581-4ff7-421c-a5e5-2f393c408a80: 'Quotes - Quote item - Function' # Quotes - Quote item - Function
    632d61ec-196c-4a96-b8bd-da959d615344: 'Call to action text' # Call to action text
    635dd1b8-ab1c-440f-b54c-690989727da1: 'This is us - Title and text - Title' # This is us - Title and text - Title
    689f3f0a-9f76-4017-beee-13a6016f87a2: 'Related blogs' # Related blogs
    690acb2d-c87f-44da-97ba-860324b25280: E-mail # E-mail
    715cec28-5d55-448c-b73f-1eb275ab2426: Awards # Awards
    724b3d9a-ff50-4268-9ed1-2230fbaf3e5f: 'This is us - Images - Bottom image' # This is us - Images - Bottom image
    734d805d-c361-4b7a-a6dd-3bb1654bbbf9: 'Services - Service item - Service description' # Services - Service item - Service description
    809e4067-5492-4cbe-8464-826f972bad81: 'Support card phonenumber' # Support card phonenumber
    829c034c-aebe-43f4-8577-4b5a856906c6: 'Items - Logo - Logo' # Items - Logo - Logo
    832fbb7f-f7cf-4a88-b890-a300bccea951: Menus # Menus
    871ff87e-8b4c-4b8f-8030-0f18ae914bc6: 'Team member' # Team member
    1068f0ad-bdf7-431b-abe5-e603e28cdeda: 'USPS Block' # USPS Block
    1189fbd0-e46b-4556-905c-fcca40eb1e8e: 'Services - Service item - Service image' # Services - Service item - Service image
    001493aa-eb55-4f15-a2fd-7de8d1e493c5: Vacancy # Vacancy
    2031ae23-a147-440b-b172-1ef37b52ad41: 'Vacancy tags Block' # Vacancy tags Block
    2153c8bc-b4ec-4689-99a1-debf1468e5c2: 'Element link' # Element link
    2198f51d-9b08-49b2-bba1-8683008d9072: 'Case labels' # Case labels
    3191ba17-dd50-4a84-b95b-196a3f649418: 'Code embed' # Code embed
    3402f2a7-cd29-414c-b0de-43b6e34b9e5f: 'Vacancy link' # Vacancy link
    4591ff8a-d908-40b6-a73c-e54b9d7c3157: 'Team member' # Team member
    05043f2c-1afb-4227-80ca-a29a82e62983: Intro # Intro
    5720c7eb-5a37-428d-bd9a-7a90410a75cf: 'Generic text field' # Generic text field
    6279fedb-05e8-44b2-9247-204d2381479b: Logo # Logo
    06746f02-f334-42a7-bd65-fe65b99d719b: 'USP grid' # USP grid
    6752ff7d-1a0a-4b87-bf08-5d4ac735a468: 'Footer socials - TikTok' # Footer socials - TikTok
    6850c800-68a8-41f1-99d2-61be158f7096: 'Case description' # Case description
    6886db96-455b-42c0-a424-9c5366619686: 'Contact / Call to action' # Contact / Call to action
    7248d08e-bf2a-4a30-8507-cc2c65e8c5ac: 'Code embed' # Code embed
    7613a7bf-c6e0-47b3-ab3d-86ba2e0f86cd: 'Background colour' # Background colour
    7713a404-999e-49de-b289-e2e23218241e: Branches # Branches
    8201b155-ca5c-4ed1-be6b-cbd1c5d9aac8: Text # Text
    08284bd3-c8ee-4b38-94d7-e700f1a9c560: Other # Other
    9222dd52-5975-4cf9-b915-d16fe4a2234a: Video # Video
    9311ee02-7e48-4bf8-b82e-0d80f147dd6a: x # x
    9518f602-f683-4746-94c7-834161d0161f: 'Menus Block' # Menus Block
    32455d9c-de7a-4457-9807-409fdeb87b9c: Link # Link
    32556adb-e3e5-43dc-8789-08df95ec1643: 'Woordenboek categorieën' # Woordenboek categorieën
    38716bcd-ebd4-4af2-891e-ffee80b3ef07: 'Main navigation' # Main navigation
    49568d29-fce0-44a2-b417-de009e133953: 'Footer call us Block' # Footer call us Block
    50662a19-2663-468c-a677-42f237d599cc: 'This is us - Title and text - Text' # This is us - Title and text - Text
    51822ed5-fb07-4d91-9233-cf8e8992ea86: 'Quote item' # Quote item
    61946b9d-5ab6-40da-8dc7-fcafbca2b556: Navigation # Navigation
    68697b02-3595-43db-9de2-6bea415a7cd3: 'Footer legal stuff - Link' # Footer legal stuff - Link
    78085d7f-d6cb-48d5-9db8-dae7b05363c2: Footer # Footer
    82910b3e-7ff3-4e83-98d9-68c20c8b9711: Services # Services
    87015ea0-4510-4561-9def-92de1e813f4b: 'Awards and Partners' # Awards and Partners
    195948dd-cdfa-4868-8d73-042c8c18201c: 'USP grid items' # USP grid items
    313800d9-569f-4af9-891e-05aafaaf5249: 'Workflow slider' # Workflow slider
    507785c9-a55b-42ab-88e5-b7c4120ab013: 'Pop-up - Button hover' # Pop-up - Button hover
    587049d8-dca9-4d14-8dae-be84a0d11e82: 'Slider - kvSlider - Title' # Slider - kvSlider - Title
    829393d0-595a-4895-8945-5930a3f931d5: Image # Image
    999678c6-96ce-49bb-85bc-64d163e5accf: 'Text image layout' # Text image layout
    4402769f-7430-4e56-aa1e-a01f64b1fcb9: 'Results - Result - Result' # Results - Result - Result
    09953799-ee0f-4b64-8556-9d1811c2cba2: Items # Items
    29044038-b2bb-4d16-8c9d-88196d5521af: Cases # Cases
    32590312-3a2c-4d9a-8323-a4de3dca22dc: Partner # Partner
    32632096-f6cf-4fb0-b7c0-46e86bce5031: 'Quotes slider' # Quotes slider
    54902690-4fb9-4a42-a112-26e6b8b1a6b7: 'Team member' # Team member
    79816237-9124-4992-b171-280645b9abe4: Images # Images
    a1a77965-214c-4eee-b96b-376d23fd8b6b: FAQ # FAQ
    a1b2c3d4-e5f6-4890-abcd-ef1234567890: 'Text video layout' # Text video layout
    a2dbf537-3f70-40e5-abec-94f2f9d7f936: New # New
    a3e5784d-e191-4d9e-8467-a871f2558ab7: Testimonials # Testimonials
    a4d3ea4b-d1fc-4255-bd1d-a4540b9df922: Case # Case
    a8ceac97-bb91-4df8-a085-092ca706f54f: 'Results heading' # Results heading
    a9a6227b-2664-487d-9677-4f9b6be861ba: 'Vacancy modals content - < 90% Gif' # Vacancy modals content - < 90% Gif
    a9bb70f4-e526-4c66-93b3-816cc7a217fb: Partners # Partners
    a58bc66d-025f-4ee2-9eda-597dcc6c7be2: 'Services - Service item - Service title' # Services - Service item - Service title
    a285e4bc-149d-4d6a-a866-271aaf6729d7: 'Call to action link' # Call to action link
    a1920b37-a9af-4b81-b980-dd3869f6c9dd: 'Case type' # Case type
    a8960dfa-04a1-4c28-9e7a-a2008fc86732: 'Footer socials' # Footer socials
    aa67614e-ba06-4368-ac11-6d900f15d07c: 'Text right' # Text right
    aabf64af-e174-4bf0-a9ba-b33d9c6dfd97: 'USP grid Item' # USP grid Item
    abd71f45-de36-49fd-bf38-4abcc09ec5de: 'Four o four' # Four o four
    abf7d301-fb1c-417d-a36e-f8160647f658: Text # Text
    aca8a580-a7a3-4202-a540-dad3a0361755: 'Vacancy benefits - Icon' # Vacancy benefits - Icon
    ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: 'Redkiwi English' # Redkiwi English
    ad360542-fb98-4efb-901d-8fd696c996b2: 'Vacancy Responsibilities' # Vacancy Responsibilities
    ae1830de-aa26-4bd5-b724-0ffb4ec2b268: 'Case detail page' # Case detail page
    ae61069d-dc9b-4b96-aefb-330b2a9cf3c4: 'Vacancy Qualifications' # Vacancy Qualifications
    b1fce06f-a605-4d4d-8d0c-5252490a7d2b: 'Vacancy modals content' # Vacancy modals content
    b8b5b4a5-fdca-436f-a7c9-43d6367f1bcf: 'Services slider' # Services slider
    b8f1fef5-9d46-494c-b4a5-a20ed087ef68: 'Call to action title' # Call to action title
    b72a4431-853e-4082-8a37-89cbc7fb2be1: 'Blogs Overview' # Blogs Overview
    b84bc40f-e1e1-41e5-9f19-e57fefafe877: 'Team Overview' # Team Overview
    b87c6b9c-87f9-4d5b-8da4-2c162345a509: 'Footer new business' # Footer new business
    b2735c83-3520-4bf2-ba76-a8b7f6abcb81: 'Hide footer contact' # Hide footer contact
    b8140e95-5084-4745-886d-752e620aabe5: 'Keyvisual - Highlighted words' # Keyvisual - Highlighted words
    b18481f0-ea8f-498f-b0f0-af0ac876d5c7: Link # Link
    b35854f6-c1d7-4142-a041-accdce795420: 'Woordenboek categorieën' # Woordenboek categorieën
    b1412986-6c93-4d4f-bc2e-5eaec130f5cd: Vacancy # Vacancy
    b8680678-1b99-468c-8b7b-0826fce89fb9: 'This is us' # This is us
    ba2a0559-d95f-4a52-8742-43c64bbf064f: 'Footer title' # Footer title
    bae6fcaa-2469-4bcd-8810-65ef42c6a58d: 'Team slider' # Team slider
    bc2ea128-3b72-473c-aa04-e4009f275253: 'Toon blog artikelen' # Toon blog artikelen
    bca2935a-8044-4a2a-be85-66bef0059fe9: 'Footer address - Label' # Footer address - Label
    bcb01ace-dba6-40f2-86d4-b1d67a9d48b5: Image # Image
    bcfc2e8a-e3ad-4905-b8fe-a16a488dd511: Contact # Contact
    bd5f655f-f3fb-438d-8206-33e607e27ae9: 'This is us - Title and image - Title' # This is us - Title and image - Title
    bdc179db-4870-4c73-a106-f188b55c71cd: Text # Text
    bf90faf1-becb-468e-8992-cb679d6bdc99: 'Vacancy requirements' # Vacancy requirements
    bf896ac9-e3a0-4372-9427-aa6a348934d4: Testimonials # Testimonials
    c0d2afa8-0688-453a-843f-681216cc1508: 'Code block' # Code block
    c054816e-c0ef-4fd0-8d75-773ac7a8b808: 'Vacancy modals content - < 70% Title' # Vacancy modals content - < 70% Title
    c3a5767b-c671-4410-8105-0b7056079982: 'Contact details E-mailaddress' # Contact details E-mailaddress
    c3c45d27-d1aa-4a3f-90b7-57e24d00bdf4: 'Items - Service item - Service icon' # Items - Service item - Service icon
    c5c83dd3-2a75-4815-b605-c41f673a4071: Quote # Quote
    c6af9219-93a6-4e77-bf57-a16da9d1d6f9: 'USP Image list' # USP Image list
    c6d7af15-505f-4ee9-b659-fbe0b9e9722e: Result # Result
    c7b7368f-b2a3-4515-b8f6-1e926afe6800: 'Two column text with intro' # Two column text with intro
    c7cf17b7-91b8-41b1-b811-7feee1b3ff8d: 'Vacancy benefits Block' # Vacancy benefits Block
    c7f0d031-1fc4-4e65-b2c2-082c9bff059d: 'Text slider' # Text slider
    c99eeeff-6ff8-4cda-83c1-327575411ceb: 'Button hover' # Button hover
    c161e1ce-fb8b-411c-9ab4-b8e281e68ab7: 'Uitgelichte blog artikelen' # Uitgelichte blog artikelen
    c181e5f9-f45c-4e22-9231-0b1d9b4902c2: 'Text Image' # Text Image
    c181e5f9-f45c-4e22-9231-0b1d9b4902c3: 'Text Video' # Text Video
    c444b62c-e48c-4eae-a9d6-f7499ad0aa0f: Contact # Contact
    c469a399-07d6-44b9-96fe-6a13e7890b39: 'Intro Text - Highlighted words' # Intro Text - Highlighted words
    c831fc1b-388a-4ed5-8432-8f2f181da4e6: 'Custom social icon' # Custom social icon
    c992a6c5-31c2-48f9-9c71-4149ef842233: Case/Partner # Case/Partner
    c9648e23-ae35-462e-88f5-8a8776614293: 'Blog labels' # Blog labels
    ca8e08a9-5198-49c6-9b11-ecab6c05f953: Service # Service
    cab89efd-9d37-421a-8bb2-910ce5ee891e: 'Vacancy modals content - < 90% Text' # Vacancy modals content - < 90% Text
    cb9dd96d-3d36-4051-826a-685f846f1ed4: 'Vacancy requirements Block' # Vacancy requirements Block
    cb904b59-215b-4044-b20e-f4bef9db835d: Doorways # Doorways
    cc2b1f33-a2c6-4e75-923e-f059c479c8cd: 'Has page' # Has page
    cc7bbf93-6233-474d-a550-0352b8237506: 'This is us - Title and image - Image' # This is us - Title and image - Image
    ccc07e22-76da-45b2-a3ae-f65bb0e67e2c: 'Link - Hover text' # Link - Hover text
    cd21c9da-13cb-49ea-b662-4f457ed58d51: 'Vacancy Work Hours' # Vacancy Work Hours
    cd81d0a2-61ff-4127-b7b4-a8acb68c1f32: Default # Default
    cd10794d-2efc-4566-9ba0-35e668e1a7ae: 'Multiple links - Link' # Multiple links - Link
    cdac2931-0ec0-40fc-ae2d-6fe345be5493: 'Fallback image' # Fallback image
    ce994d9b-3707-41f3-98eb-7774d899d7a7: 'Case label' # Case label
    cf650f85-3b32-4b5a-9af4-4d65f35e804d: 'Vacancy modals content - < 70% Text' # Vacancy modals content - < 70% Text
    d0ea42bf-f2d4-489f-a8d5-ad65cc91adc8: kvSlider # kvSlider
    d1b3bc0a-9b34-409f-966c-2a57604bf509: 'Company region' # Company region
    d1c3e964-b35c-4fb5-836f-8f0039b73015: 'Slider - kvSlider - CTA' # Slider - kvSlider - CTA
    d1d138b0-7884-4c65-9b42-cc7e09e4abda: 'Pop-up - Title' # Pop-up - Title
    d6d5891e-cfff-450a-ac2a-94a4e1395de4: 'Vacancy Employment Type' # Vacancy Employment Type
    d7f8dd8f-0e37-4f5a-9dd1-0c2fe7567901: Sliders # Sliders
    d8ab3984-29dd-489c-bc57-b3aef2d29ec2: 'Blog detail pages' # Blog detail pages
    d8f42343-9e51-4569-8257-0bc5783d8305: Heading # Heading
    d42dd6bd-a431-448b-b5c2-ca5646b78b9f: 'Text Only' # Text Only
    d62e1982-32a4-4b26-994b-ef66ce2fc85a: 'Menus - Links' # Menus - Links
    d65df4e4-680d-41a3-82a6-80bbe728be5b: 'Service item' # Service item
    d558fa50-af1a-4b56-9880-2ae23253d4da: 'This is us - Images - Top image' # This is us - Images - Top image
    d772776f-6009-402d-bc9b-b869a81ca44c: 'Vacancy tags - Tag' # Vacancy tags - Tag
    dacae371-f770-4652-9a02-d5e36427abf6: 'Job title' # Job title
    dbab1adf-3534-439d-80cf-80f700c9346a: 'Vacancy modals content - Perfect match gif' # Vacancy modals content - Perfect match gif
    dc16708b-a749-419e-be62-94cf1251115e: 'Footer legal stuff Block' # Footer legal stuff Block
    dc44344a-72a8-4d32-b34f-e96749028cb8: 'Vacancy secondary link' # Vacancy secondary link
    dcd27536-3587-49bb-a07e-f4e7babfb550: Title # Title
    df5b68ef-1ea5-4712-93a2-b94015ab1ac3: 'Multiple links Block' # Multiple links Block
    e07d63a2-4e0a-4837-b422-d1c26d4d9b60: 'Items - Service item - Service title' # Items - Service item - Service title
    e1ef25b3-1160-4df7-907e-fd1c3d0bd77b: Woordenboek # Woordenboek
    e2aae430-8aef-4ee2-82a3-e636e91bfacd: Auteur # Auteur
    e5d60224-e78d-46ac-9db3-c989e24b6e06: 'Footer slider link' # Footer slider link
    e6c24a32-f451-458d-b6d1-d570ed78a644: 'USP image list' # USP image list
    e7d63ef0-2e58-40f7-9c3a-0e83ecce5020: Partner # Partner
    e47b595a-6021-486b-9ec8-3b034f95b6f2: 'Case Award' # Case Award
    e54af7b7-7f7c-4e7c-8346-ec0710126eec: 'Link Only' # Link Only
    e525ae1d-fb06-450c-88c8-ebb1f2e1b3e6: 'Support card title' # Support card title
    e533f192-df51-4930-80d1-9a0900771d78: 'Call to action link Block' # Call to action link Block
    e2821c62-718f-48bc-a360-ef173f449f4e: 'Blog labels' # Blog labels
    e4937c7e-8a69-4d07-9222-5d609fa142de: 'Cases Overview' # Cases Overview
    e8197d66-ecf5-491c-a1f8-a628305e0dbf: Images # Images
    e24238a5-679a-4302-b94d-bc3a4d19afeb: Whatsapp # Whatsapp
    e4585986-6c69-4ed4-bfa7-3718567486b2: 'Support card e-mailaddress' # Support card e-mailaddress
    e9956651-6a06-4e5a-90b6-ecef2f35b81f: Pages # Pages
    ea24b157-0fef-454f-b0fb-19e50852498f: 'Custom social label' # Custom social label
    eab378be-e31e-41ef-9a97-d0bac74bc431: 'Accountmanagers subtitle' # Accountmanagers subtitle
    ebab13af-c430-4edf-b85f-0a73e93040a3: Image # Image
    ebbd9bf5-3e66-40cc-8e2a-d535af9272b5: 'Blogs Overview' # Blogs Overview
    ec2421e2-8288-4768-9e17-4ede4df1f558: Services # Services
    ed13084c-dea4-4ca3-a402-bd59c634727c: 'Contact form' # Contact form
    eff8f77a-631a-4e53-b397-4502ff2567f3: 'Vacancy Job Benefits' # Vacancy Job Benefits
    f023b5a8-2699-469c-b0a8-c5f4f83d8c87: 'Slider text' # Slider text
    f4df22b8-85ff-4bf5-91c4-0800735812fa: 'Vacancy modals content - Perfect match title' # Vacancy modals content - Perfect match title
    f16c6604-ba80-425a-91ba-f63c2cd6cede: 'Pop-up - Image' # Pop-up - Image
    f56fbef8-cbb9-41e9-a487-1e2832be591d: 'Intro Text Block' # Intro Text Block
    f126c99b-c346-4da4-a351-fdf4b6282b91: 'Background color' # Background color
    f509b14b-9f99-4736-83ce-062f496c4928: 'Slider - kvSlider - Video url' # Slider - kvSlider - Video url
    f5805ef3-c3ba-49af-85ab-5c56a50d4516: 'Call to action link - Link' # Call to action link - Link
    f498369c-482d-48ad-9e3b-978b477b8108: FAQ # FAQ
    f5570141-ebba-481e-8203-706639af1681: 'Footer address Block' # Footer address Block
    fad26ab4-0611-4970-b9f6-40d15e146e21: 'Phone number' # Phone number
    fbf4cd2d-078e-4a42-95df-460eab9edf53: 'Custom modal content' # Custom modal content
    fca1dd77-8d6b-4617-b18e-2ecd16e01018: 'Footer links - Entry' # Footer links - Entry
    fccbdd25-7231-4302-ab62-f4f72e67c688: Services # Services
    fda30e8d-8d2b-475a-b382-229c71f3bfc7: Pop-up # Pop-up
    fdf2b4c9-be6f-4fd1-87f1-c48c2ea32392: 'Partners backdrop' # Partners backdrop
    fe4b011a-6aca-4701-9c91-c481f03feacb: 'Partner subtitle' # Partner subtitle
    fe8e4a8b-aeaf-459d-a167-127bc858bb07: Woordenboek # Woordenboek
plugins:
  admin-bar:
    edition: standard
    enabled: true
    schemaVersion: 3.1.0
  architect:
    edition: standard
    enabled: true
    schemaVersion: 2.0.0
  ckeditor:
    edition: standard
    enabled: true
    schemaVersion: *******
  cookies:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  cp-field-inspect:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  craft-meilisearch:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  embeddedassets:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  feed-me:
    edition: standard
    enabled: true
    schemaVersion: 5.1.0.0
  formie:
    edition: standard
    enabled: true
    licenseKey: LCYSXISR4F0XLXVKWAE7NID0
    schemaVersion: 3.4.8
    settings:
      ajaxTimeout: 10
      alertEmails: null
      captchas:
        __assoc__:
          -
            - recaptcha
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Recaptcha
                -
                  - enabled
                  - '1'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      0:
                        - handle
                        - recaptcha
                      1:
                        - secretKey
                        - $GOOGLE_RECAPTCHA_SECRET_KEY
                      2:
                        - siteKey
                        - $GOOGLE_RECAPTCHA_SITE_KEY
                      3:
                        - type
                        - v3
                      4:
                        - size
                        - normal
                      5:
                        - theme
                        - light
                      6:
                        - badge
                        - bottomright
                      7:
                        - language
                        - en
                      8:
                        - minScore
                        - 0.5
                      10:
                        - scriptLoadingMethod
                        - asyncDefer
          -
            - hcaptcha
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Hcaptcha
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      0:
                        - handle
                        - hcaptcha
                      3:
                        - size
                        - normal
                      4:
                        - theme
                        - light
                      5:
                        - language
                        - en
                      6:
                        - minScore
                        - 0.5
                      7:
                        - scriptLoadingMethod
                        - asyncDefer
          -
            - friendlyCaptcha
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\FriendlyCaptcha
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - friendlyCaptcha
          -
            - turnstile
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Turnstile
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      0:
                        - handle
                        - turnstile
                      3:
                        - scriptLoadingMethod
                        - asyncDefer
          -
            - duplicate
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Duplicate
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - duplicate
          -
            - honeypot
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Honeypot
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - honeypot
          -
            - javascript
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Javascript
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - javascript
      defaultDateDisplayType: calendar
      defaultDateTime: null
      defaultDateValueOption: ''
      defaultEmailTemplate: ''
      defaultFileUploadVolume: ''
      defaultFormTemplate: ''
      defaultInstructionsPosition: verbb\formie\positions\AboveInput
      defaultLabelPosition: verbb\formie\positions\AboveInput
      defaultPage: forms
      emptyValuePlaceholder: 'No response.'
      enableBackSubmission: true
      enableCsrfValidationForGuests: true
      enableLargeFieldStorage: false
      enableUnloadWarning: true
      includeDraftElementUsage: false
      includeRevisionElementUsage: false
      maxIncompleteSubmissionAge: 30
      maxSentNotificationsAge: 30
      pdfPaperOrientation: portrait
      pdfPaperSize: letter
      pluginName: Formie
      queuePriority: null
      saveSpam: false
      sendEmailAlerts: false
      sentNotifications: true
      spamBehaviour: showSuccess
      spamBehaviourMessage: ''
      spamEmailNotifications: false
      spamKeywords: ''
      spamLimit: 500
      useQueueForIntegrations: true
      useQueueForNotifications: true
  geomate:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  hyper:
    edition: standard
    enabled: true
    licenseKey: VQKQ7JLKR8IAD19TJ1S5XQ1H
    schemaVersion: 1.0.0
  image-resizer:
    edition: standard
    enabled: true
    schemaVersion: 2.0.0
  navigation:
    edition: standard
    enabled: true
    licenseKey: C7GPUFLI7VZWA0AHJM4C45KR
    schemaVersion: 2.1.0
  neo:
    edition: standard
    enabled: true
    licenseKey: J0Z31VKW4SNDVXKHH54VI50Y
    schemaVersion: 5.1.0.1
    settings:
      blockTypeIconSources: '*'
      collapseAllBlocks: false
      defaultAlwaysShowGroupDropdowns: true
      enableBlockTypeUserPermissions: true
      newBlockMenuStyle: classic
      optimiseSearchIndexing: true
  redactor:
    edition: standard
    enabled: true
    schemaVersion: 2.3.0
  retour:
    edition: standard
    enabled: true
    licenseKey: TGU8WPQRG06S310Z9A3FH972
    schemaVersion: 3.0.12
  seomatic:
    edition: standard
    enabled: true
    licenseKey: Q0F63HNI5GEUA821KG7HK26E
    schemaVersion: 3.0.13
    settings:
      addHrefLang: true
      addPaginatedHreflang: true
      addXDefaultHrefLang: true
      alwaysIncludeCanonicalUrls: false
      cpTitlePrefix: '⚙ '
      cspNonce: ''
      cspScriptSrcPolicies:
        -
          __assoc__:
            -
              - policy
              - '''self'''
      devModeCpTitlePrefix: '&#x1f6a7;⚙ '
      devModeTitlePrefix: '&#x1f6a7; '
      displayAnalysisSidebar: true
      displayPreviewSidebar: true
      enableJsonLdEndpoint: false
      enableMetaContainerEndpoint: false
      enableSeoFileLinkEndpoint: false
      environment: live
      excludeNonCanonicalUrls: false
      generatorEnabled: true
      headersEnabled: true
      includeHomepageInBreadcrumbs: true
      lowercaseCanonicalUrl: true
      manuallySetEnvironment: false
      maxDescriptionLength: 155
      maxTitleLength: 70
      metaCacheDuration: 0
      pluginName: SEOmatic
      regenerateSitemapsAutomatically: true
      renderEnabled: true
      separatorChar: '|'
      sidebarDisplayPreviewTypes:
        - google
        - twitter
        - facebook
      siteGroupsSeparate: true
      siteUrlOverride: ''
      sitemapsEnabled: true
      socialMediaPreviewTarget: true
      submitSitemaps: true
      truncateDescriptionTags: true
      truncateTitleTags: true
  smith:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  templatecomments:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  typedlinkfield:
    edition: standard
    enabled: true
    schemaVersion: 2.0.0
  vite:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
system:
  edition: pro
  live: true
  name: Redkiwi
  schemaVersion: *******
  timeZone: America/Los_Angeles
