columnSuffix: null
handle: relatedBlogs
instructions: null
name: 'Related blogs'
searchable: false
settings:
  allowSelfRelations: false
  branchLimit: null
  defaultPlacement: end
  maintainHierarchy: false
  maxRelations: null
  minRelations: null
  selectionLabel: 'Select a blog'
  showCardsInGrid: false
  showSiteMenu: false
  showUnpermittedEntries: true
  showUnpermittedSections: true
  sources:
    - 'section:d8ab3984-29dd-489c-bc57-b3aef2d29ec2' # Blog detail pages
  targetSiteId: null
  validateRelatedElements: false
  viewMode: null
translationKeyFormat: null
translationMethod: none
type: craft\fields\Entries
