columnSuffix: jwyuwoug
handle: textImageLayout
instructions: 'Defines the layout on desktop. On mobile the image will always be shown before the text.'
name: 'Text image layout'
searchable: false
settings:
  customOptions: false
  options:
    -
      __assoc__:
        -
          - label
          - 'Text left / Image right'
        -
          - value
          - text-image
        -
          - default
          - '1'
    -
      __assoc__:
        -
          - label
          - 'Image left / Text right'
        -
          - value
          - image-text
        -
          - default
          - ''
translationKeyFormat: null
translationMethod: site
type: craft\fields\Dropdown
