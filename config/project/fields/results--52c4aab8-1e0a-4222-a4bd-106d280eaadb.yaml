columnSuffix: null
handle: results
instructions: '5 possible results to display in the right column. The visit option is mandatory.'
name: Results
searchable: false
settings:
  createButtonLabel: null
  defaultIndexViewMode: cards
  enableVersioning: false
  entryTypes:
    -
      __assoc__:
        -
          - uid
          - 32ee1ae3-113b-47ae-bcbb-5f042cc399f2 # Visit
    -
      __assoc__:
        -
          - uid
          - c6d7af15-505f-4ee9-b659-fbe0b9e9722e # Result
  includeTableView: false
  maxEntries: 5
  minEntries: 1
  pageSize: null
  propagationKeyFormat: null
  propagationMethod: siteGroup
  showCardsInGrid: false
  viewMode: blocks
translationKeyFormat: null
translationMethod: site
type: craft\fields\Matrix
