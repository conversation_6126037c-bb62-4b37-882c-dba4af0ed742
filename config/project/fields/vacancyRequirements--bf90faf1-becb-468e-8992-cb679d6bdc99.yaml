columnSuffix: null
handle: vacancyRequirements
instructions: null
name: 'Vacancy requirements'
searchable: false
settings:
  createButtonLabel: null
  defaultIndexViewMode: cards
  enableVersioning: false
  entryTypes:
    -
      __assoc__:
        -
          - uid
          - cb9dd96d-3d36-4051-826a-685f846f1ed4 # Vacancy requirements Block
  includeTableView: false
  maxEntries: null
  minEntries: 1
  pageSize: null
  propagationKeyFormat: null
  propagationMethod: none
  showCardsInGrid: false
  viewMode: blocks
translationKeyFormat: null
translationMethod: site
type: craft\fields\Matrix
