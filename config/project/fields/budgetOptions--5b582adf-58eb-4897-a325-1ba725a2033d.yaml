columnSuffix: null
handle: budgetOptions
instructions: null
name: 'Budget options'
searchable: false
settings:
  addRowLabel: 'Add an option'
  columns:
    __assoc__:
      -
        - col1
        -
          __assoc__:
            -
              - heading
              - Option
            -
              - handle
              - option
            -
              - width
              - ''
            -
              - type
              - singleline
      -
        - col2
        -
          __assoc__:
            -
              - heading
              - Value
            -
              - handle
              - value
            -
              - width
              - ''
            -
              - type
              - singleline
  defaults:
    -
      __assoc__:
        -
          - col1
          - '10 - 20k'
        -
          - col2
          - 10-20
    -
      __assoc__:
        -
          - col1
          - '20 - 40k'
        -
          - col2
          - 20-40
  maxRows: null
  minRows: 1
  staticRows: false
translationKeyFormat: null
translationMethod: site
type: craft\fields\Table
