columnSuffix: null
handle: relatedBlogLabel
instructions: 'Select from which category (blog label) you want to show related blogs. If this value is empty, the first category (blog label) is used to display related blogs.'
name: 'Related blog label'
searchable: false
settings:
  allowSelfRelations: false
  branchLimit: null
  defaultPlacement: end
  maintainHierarchy: false
  maxRelations: 1
  minRelations: null
  selectionLabel: 'Select blog label'
  showCardsInGrid: false
  showSiteMenu: true
  showUnpermittedEntries: true
  showUnpermittedSections: true
  sources:
    - 'section:c9648e23-ae35-462e-88f5-8a8776614293' # Blog labels
  targetSiteId: null
  validateRelatedElements: false
  viewMode: null
translationKeyFormat: null
translationMethod: site
type: craft\fields\Entries
