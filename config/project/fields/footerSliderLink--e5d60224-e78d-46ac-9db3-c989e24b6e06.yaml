columnSuffix: null
handle: footerSliderLink
instructions: null
name: 'Footer slider link'
searchable: false
settings:
  allowSelfRelations: false
  branchLimit: null
  defaultPlacement: end
  maintainHierarchy: false
  maxRelations: 1
  minRelations: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Entry
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\entries\EntryCondition
      -
        - conditionRules
        -
          -
            __assoc__:
              -
                - class
                - craft\elements\conditions\entries\ViewableConditionRule
              -
                - uid
                - 380c476c-eda4-4afc-b39e-e6a6b4b94748
              -
                - value
                - true
  selectionLabel: 'Select page to link to'
  showCardsInGrid: false
  showSiteMenu: false
  showUnpermittedEntries: true
  showUnpermittedSections: true
  sources: '*'
  targetSiteId: null
  validateRelatedElements: false
  viewMode: null
translationKeyFormat: null
translationMethod: none
type: craft\fields\Entries
