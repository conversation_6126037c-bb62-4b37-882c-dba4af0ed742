columnSuffix: tgltrwlb
handle: contactQuestionOptions
instructions: null
name: 'Contact question options'
searchable: false
settings:
  addRowLabel: 'Add an option'
  columns:
    __assoc__:
      -
        - col1
        -
          __assoc__:
            -
              - heading
              - Department
            -
              - handle
              - department
            -
              - width
              - ''
            -
              - type
              - singleline
      -
        - col2
        -
          __assoc__:
            -
              - heading
              - Value
            -
              - handle
              - value
            -
              - width
              - ''
            -
              - type
              - singleline
  defaults:
    -
      __assoc__:
        -
          - col1
          - Support
        -
          - col2
          - support
    -
      __assoc__:
        -
          - col1
          - 'Your own accountmanager'
        -
          - col2
          - accountmanager
  maxRows: null
  minRows: 2
  staticRows: false
translationKeyFormat: null
translationMethod: site
type: craft\fields\Table
