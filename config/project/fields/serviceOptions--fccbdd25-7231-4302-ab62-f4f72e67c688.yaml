columnSuffix: null
handle: serviceOptions
instructions: null
name: Services
searchable: false
settings:
  addRowLabel: 'Add an option'
  columns:
    __assoc__:
      -
        - col1
        -
          __assoc__:
            -
              - heading
              - Service
            -
              - handle
              - service
            -
              - width
              - ''
            -
              - type
              - singleline
      -
        - col2
        -
          __assoc__:
            -
              - heading
              - Value
            -
              - handle
              - value
            -
              - width
              - ''
            -
              - type
              - singleline
  defaults:
    -
      __assoc__:
        -
          - col1
          - Strategy
        -
          - col2
          - strategy
    -
      __assoc__:
        -
          - col1
          - Marketing
        -
          - col2
          - marketing
    -
      __assoc__:
        -
          - col1
          - Creativity
        -
          - col2
          - creativity
    -
      __assoc__:
        -
          - col1
          - Technology
        -
          - col2
          - technology
    -
      __assoc__:
        -
          - col1
          - 'Full service'
        -
          - col2
          - full-service
    -
      __assoc__:
        -
          - col1
          - Other...
        -
          - col2
          - other
  maxRows: null
  minRows: 2
  staticRows: false
translationKeyFormat: null
translationMethod: site
type: craft\fields\Table
