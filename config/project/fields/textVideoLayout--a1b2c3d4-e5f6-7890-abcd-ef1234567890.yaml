columnSuffix: txvdlyot
contentColumnType: string
handle: textVideoLayout
instructions: 'Defines the layout on desktop. On mobile the video will always be shown before the text.'
name: 'Text video layout'
searchable: false
settings:
  columnType: string
  options:
    -
      __assoc__:
        -
          - label
          - 'Text left / Video right'
        -
          - value
          - text-video
        -
          - default
          - '1'
    -
      __assoc__:
        -
          - label
          - 'Video left / Text right'
        -
          - value
          - video-text
        -
          - default
          - ''
translationKeyFormat: null
translationMethod: site
type: craft\fields\Dropdown
