columnSuffix: null
handle: fallbackImage
instructions: 'This is the image that will be shown in case that the user has not accepted cookies.'
name: 'Fallback image'
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds:
    - image
  branchLimit: null
  defaultPlacement: end
  defaultUploadLocationSource: 'volume:79816237-9124-4992-b171-280645b9abe4' # Images
  defaultUploadLocationSubpath: null
  maintainHierarchy: false
  maxRelations: null
  minRelations: 1
  previewMode: full
  restrictFiles: true
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:79816237-9124-4992-b171-280645b9abe4' # Images
  restrictedLocationSubpath: null
  selectionLabel: null
  showCardsInGrid: false
  showSiteMenu: true
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  sources:
    - 'volume:79816237-9124-4992-b171-280645b9abe4' # Images
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Assets
