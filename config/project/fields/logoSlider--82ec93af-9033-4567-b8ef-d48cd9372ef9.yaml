columnSuffix: null
handle: logoSlider
instructions: 'Keep in mind that the scrolling animation is dependant on the width of all the images and tags combined. So when using two rows try to balance wide and shorter images over the two rows. Make sure the rows also feature a similar amount of items for the best results.'
name: 'Logo slider'
searchable: false
settings:
  createButtonLabel: null
  defaultIndexViewMode: cards
  enableVersioning: false
  entryTypes:
    -
      __assoc__:
        -
          - uid
          - 63fa796f-8450-4f7f-8a97-e297f5b32e23 # Logo slider Block
  includeTableView: false
  maxEntries: 2
  minEntries: 1
  pageSize: null
  propagationKeyFormat: null
  propagationMethod: all
  showCardsInGrid: false
  viewMode: blocks
translationKeyFormat: null
translationMethod: site
type: craft\fields\Matrix
