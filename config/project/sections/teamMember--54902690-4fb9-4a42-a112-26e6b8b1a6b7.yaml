defaultPlacement: end
enableVersioning: true
entryTypes:
  -
    uid: 871ff87e-8b4c-4b8f-8030-0f18ae914bc6 # Team member
handle: teamMember
maxAuthors: 1
name: 'Team member'
previewTargets:
  -
    __assoc__:
      -
        - label
        - 'Primary entry page'
      -
        - urlFormat
        - '{url}'
      -
        - refresh
        - '1'
propagationMethod: siteGroup
siteSettings:
  4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: # Redkiwi Dutch
    enabledByDefault: true
    hasUrls: true
    template: pages/team-member.twig
    uriFormat: 'team/{slug}'
  ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: # Redkiwi English
    enabledByDefault: true
    hasUrls: true
    template: pages/team-member.twig
    uriFormat: 'team/{slug}'
structure:
  maxLevels: 1
  uid: e04c57ab-aa0c-412c-935b-a41349a6d13e
type: structure
