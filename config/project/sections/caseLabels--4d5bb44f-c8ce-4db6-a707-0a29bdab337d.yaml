defaultPlacement: end
enableVersioning: false
entryTypes:
  -
    uid: ce994d9b-3707-41f3-98eb-7774d899d7a7 # Case label
handle: caseLabels
maxAuthors: <AUTHORS>
name: 'Case labels'
previewTargets:
  -
    __assoc__:
      -
        - label
        - 'Primary entry page'
      -
        - urlFormat
        - '{url}'
      -
        - refresh
        - '1'
propagationMethod: siteGroup
siteSettings:
  4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: # Redkiwi Dutch
    enabledByDefault: true
    hasUrls: false
    template: null
    uriFormat: null
  ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: # Redkiwi English
    enabledByDefault: true
    hasUrls: false
    template: null
    uriFormat: null
structure:
  maxLevels: 1
  uid: 9e83a8cd-f3d1-430a-886b-e16e722086dc
type: structure
