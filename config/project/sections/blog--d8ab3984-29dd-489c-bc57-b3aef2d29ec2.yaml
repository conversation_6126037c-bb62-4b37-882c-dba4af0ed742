defaultPlacement: end
enableVersioning: true
entryTypes:
  -
    uid: 0f6215ea-d16e-4196-937d-0a04d31672aa # Blog
handle: blog
maxAuthors: <AUTHORS>
name: 'Blog detail pages'
previewTargets:
  -
    __assoc__:
      -
        - label
        - 'Primary entry page'
      -
        - urlFormat
        - '{url}'
      -
        - refresh
        - '1'
propagationMethod: siteGroup
siteSettings:
  4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: # Redkiwi Dutch
    enabledByDefault: true
    hasUrls: true
    template: pages/blog.twig
    uriFormat: 'kennis/{slug}'
  ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: # Redkiwi English
    enabledByDefault: true
    hasUrls: true
    template: pages/blog.twig
    uriFormat: 'insights/{slug}'
type: channel
