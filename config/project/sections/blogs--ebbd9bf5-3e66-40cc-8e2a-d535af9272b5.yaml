defaultPlacement: end
enableVersioning: true
entryTypes:
  -
    uid: b72a4431-853e-4082-8a37-89cbc7fb2be1 # Blogs Overview
handle: blogs
maxAuthors: <AUTHORS>
name: 'Blogs Overview'
previewTargets:
  -
    __assoc__:
      -
        - label
        - 'Primary entry page'
      -
        - urlFormat
        - '{url}'
      -
        - refresh
        - '1'
propagationMethod: all
siteSettings:
  4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: # Redkiwi Dutch
    enabledByDefault: true
    hasUrls: true
    template: archive/blogs-overview.twig
    uriFormat: kennis
  ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: # Redkiwi English
    enabledByDefault: true
    hasUrls: true
    template: archive/blogs-overview.twig
    uriFormat: insights
type: single
