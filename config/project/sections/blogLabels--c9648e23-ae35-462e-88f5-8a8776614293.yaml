defaultPlacement: end
enableVersioning: false
entryTypes:
  -
    uid: 8dcf96b2-9fc2-4be3-92c2-8c581f16c4d0 # Blog label
handle: blogLabels
maxAuthors: <AUTHORS>
name: 'Blog labels'
previewTargets:
  -
    __assoc__:
      -
        - label
        - 'Primary entry page'
      -
        - urlFormat
        - '{url}'
      -
        - refresh
        - '1'
propagationMethod: siteGroup
siteSettings:
  4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: # Redkiwi Dutch
    enabledByDefault: true
    hasUrls: false
    template: null
    uriFormat: null
  ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: # Redkiwi English
    enabledByDefault: true
    hasUrls: false
    template: null
    uriFormat: null
structure:
  maxLevels: 1
  uid: 9e2cc9af-cc80-481f-9536-e00f8d303f08
type: structure
