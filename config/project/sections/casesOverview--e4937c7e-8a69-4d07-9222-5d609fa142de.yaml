defaultPlacement: end
enableVersioning: true
entryTypes:
  -
    uid: 0e1fe906-02d3-4a9b-8966-37b2f3adb1db # Cases Overview
handle: casesOverview
maxAuthors: <AUTHORS>
name: 'Cases Overview'
previewTargets:
  -
    __assoc__:
      -
        - label
        - 'Primary entry page'
      -
        - urlFormat
        - '{url}'
      -
        - refresh
        - '1'
propagationMethod: all
siteSettings:
  4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: # Redkiwi Dutch
    enabledByDefault: true
    hasUrls: true
    template: archive/cases-overview.twig
    uriFormat: cases
  ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: # Redkiwi English
    enabledByDefault: true
    hasUrls: true
    template: archive/cases-overview.twig
    uriFormat: cases
type: single
