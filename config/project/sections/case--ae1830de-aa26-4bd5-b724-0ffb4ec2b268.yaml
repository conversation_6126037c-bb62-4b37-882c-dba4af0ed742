defaultPlacement: end
enableVersioning: true
entryTypes:
  -
    uid: a4d3ea4b-d1fc-4255-bd1d-a4540b9df922 # Case
handle: case
maxAuthors: 1
name: 'Case detail page'
previewTargets:
  -
    __assoc__:
      -
        - label
        - 'Primary entry page'
      -
        - urlFormat
        - '{url}'
      -
        - refresh
        - '1'
propagationMethod: siteGroup
siteSettings:
  4ca4c6c4-246d-4fb4-9b85-018d7a8e8790: # Redkiwi Dutch
    enabledByDefault: true
    hasUrls: true
    template: pages/case.twig
    uriFormat: 'cases/{slug}'
  ad67b40a-6f1a-4daf-afec-3ba59b5e04c9: # Redkiwi English
    enabledByDefault: true
    hasUrls: true
    template: pages/case.twig
    uriFormat: 'cases/{slug}'
structure:
  maxLevels: 1
  uid: ea47024b-7eae-4ec2-8438-abc912e618bb
type: structure
