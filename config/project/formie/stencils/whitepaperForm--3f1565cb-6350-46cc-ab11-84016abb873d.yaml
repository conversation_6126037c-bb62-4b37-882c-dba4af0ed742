data:
  dataRetention: forever
  dataRetentionValue: null
  fileUploadsAction: retain
  pages:
    -
      label: 'Page 1'
      rows:
        -
          fields:
            -
              settings:
                conditions: null
                containerAttributes: null
                contentTable: null
                cssClasses: null
                defaultValue: null
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: firstName
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: verbb\formie\positions\AboveInput
                label: Name
                labelPosition: null
                matchField: null
                nestedLayoutId: null
                placeholder: null
                prePopulate: null
                required: true
                rows:
                  -
                    fields:
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: false
                          errorMessage: null
                          handle: prefix
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: honorific-prefix
                          instructions: null
                          instructionsPosition: null
                          label: Prefix
                          labelPosition: null
                          layout: null
                          limitOptions: false
                          matchField: null
                          max: null
                          min: null
                          multi: false
                          optgroups: true
                          options:
                            -
                              label: 'Select an option'
                              value: ''
                            -
                              label: Mr.
                              value: mr
                            -
                              label: Mrs.
                              value: mrs
                            -
                              label: Ms.
                              value: ms
                            -
                              label: Miss.
                              value: miss
                            -
                              label: Mx.
                              value: mx
                            -
                              label: Dr.
                              value: dr
                            -
                              label: Prof.
                              value: prof
                          placeholder: null
                          prePopulate: null
                          required: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NamePrefix
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: true
                          errorMessage: null
                          handle: firstName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: given-name
                          instructions: null
                          instructionsPosition: null
                          label: 'First Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameFirst
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: false
                          errorMessage: null
                          handle: middleName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: additional-name
                          instructions: null
                          instructionsPosition: null
                          label: 'Middle Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameMiddle
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: true
                          errorMessage: null
                          handle: lastName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: family-name
                          instructions: null
                          instructionsPosition: null
                          label: 'Last Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameLast
                subFieldLabelPosition: null
                useMultipleFields: false
                visibility: null
              type: verbb\formie\fields\Name
        -
          fields:
            -
              settings:
                conditions: null
                containerAttributes: null
                contentTable: null
                cssClasses: null
                defaultValue: null
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: surname
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: verbb\formie\positions\AboveInput
                label: Surname
                labelPosition: null
                matchField: null
                nestedLayoutId: null
                placeholder: null
                prePopulate: null
                required: true
                rows:
                  -
                    fields:
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: false
                          errorMessage: null
                          handle: prefix
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: honorific-prefix
                          instructions: null
                          instructionsPosition: null
                          label: Prefix
                          labelPosition: null
                          layout: null
                          limitOptions: false
                          matchField: null
                          max: null
                          min: null
                          multi: false
                          optgroups: true
                          options:
                            -
                              label: 'Select an option'
                              value: ''
                            -
                              label: Mr.
                              value: mr
                            -
                              label: Mrs.
                              value: mrs
                            -
                              label: Ms.
                              value: ms
                            -
                              label: Miss.
                              value: miss
                            -
                              label: Mx.
                              value: mx
                            -
                              label: Dr.
                              value: dr
                            -
                              label: Prof.
                              value: prof
                          placeholder: null
                          prePopulate: null
                          required: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NamePrefix
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: true
                          errorMessage: null
                          handle: firstName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: given-name
                          instructions: null
                          instructionsPosition: null
                          label: 'First Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameFirst
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: false
                          errorMessage: null
                          handle: middleName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: additional-name
                          instructions: null
                          instructionsPosition: null
                          label: 'Middle Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameMiddle
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: true
                          errorMessage: null
                          handle: lastName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: family-name
                          instructions: null
                          instructionsPosition: null
                          label: 'Last Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameLast
                subFieldLabelPosition: null
                useMultipleFields: false
                visibility: null
              type: verbb\formie\fields\Name
        -
          fields:
            -
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: emailAddress
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                label: 'Business e-mailaddress'
                labelPosition: null
                matchField: null
                placeholder: null
                prePopulate: null
                required: true
                uniqueValue: false
                validateDomain: false
                visibility: null
              type: verbb\formie\fields\Email
        -
          fields:
            -
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: company
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                label: Company
                labelPosition: null
                limit: false
                matchField: null
                max: null
                maxType: characters
                min: null
                minType: characters
                placeholder: null
                prePopulate: null
                required: false
                uniqueValue: false
                visibility: null
              type: verbb\formie\fields\SingleLineText
        -
          fields:
            -
              settings:
                checkedValue: '1'
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: false
                description:
                  -
                    attrs:
                      textAlign: start
                    content:
                      -
                        text: 'ik ga akkoord met '
                        type: text
                      -
                        marks:
                          -
                            attrs:
                              class: null
                              href: 'https://www.redkiwi.nl/algemene-voorwaarden'
                              target: _blank
                            type: link
                        text: voorwaarden
                        type: text
                    type: paragraph
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: termsAndConditions
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                label: 'Terms and conditions'
                labelPosition: verbb\formie\positions\Hidden
                matchField: null
                placeholder: null
                prePopulate: null
                required: true
                uncheckedValue: '0'
                visibility: null
              type: verbb\formie\fields\Agree
      settings:
        backButtonLabel: Back
        buttonsPosition: left
        containerAttributes: null
        cssClasses: null
        enableJsEvents: false
        enableNextButtonConditions: true
        enablePageConditions: false
        inputAttributes: null
        nextButtonConditions:
          conditionRule: all
          showRule: show
        saveButtonLabel: Save
        saveButtonStyle: link
        showBackButton: false
        showSaveButton: false
        submitButtonLabel: 'Sign up now'
  settings:
    collectIp: false
    collectUser: false
    dataRetention: null
    dataRetentionValue: null
    defaultEmailTemplateId: null
    defaultInstructionsPosition: verbb\formie\positions\AboveInput
    defaultLabelPosition: verbb\formie\positions\AboveInput
    disableCaptchas: false
    displayCurrentPageTitle: false
    displayFormTitle: false
    displayPageProgress: false
    displayPageTabs: false
    errorMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Couldn’t save submission due to errors."}]}]'
    errorMessagePosition: top-form
    fileUploadsAction: null
    integrations:
      activeCampaign:
        accountFieldMapping:
          1: ''
          2: ''
          3: ''
          4: ''
          5: ''
          6: ''
          7: ''
          8: ''
          9: ''
          10: ''
          11: ''
          12: ''
          name: ''
        contactFieldMapping:
          1: ''
          2: ''
          8: ''
          9: ''
          10: ''
          11: ''
          12: ''
          13: ''
          15: ''
          16: ''
          19: ''
          20: ''
          21: ''
          email: ''
          firstName: ''
          lastName: ''
          listId: ''
          phone: ''
          tags: ''
        dealFieldMapping:
          1: ''
          2: ''
          currency: ''
          description: ''
          group: ''
          owner: ''
          percent: ''
          stage: ''
          status: ''
          title: ''
          value: ''
        enabled: '1'
        mapToAccount: ''
        mapToContact: ''
        mapToDeal: ''
        optInField: ''
      recaptcha:
        enabled: '1'
        showAllPages: ''
    limitSubmissions: false
    limitSubmissionsMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    limitSubmissionsNumber: null
    limitSubmissionsType: total
    loadingIndicator: spinner
    loadingIndicatorText: null
    pageRedirectUrl: null
    progressPosition: end
    redirectUrl: null
    requireUser: false
    requireUserMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    requiredIndicator: asterisk
    scheduleForm: false
    scheduleFormEnd: null
    scheduleFormExpiredMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleFormPendingMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleFormStart: null
    scrollToTop: true
    submissionTitleFormat: '{timestamp}'
    submitAction: message
    submitActionFormHide: false
    submitActionMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"You will receive an e-mail with the whitepaper."}]}]'
    submitActionMessagePosition: top-form
    submitActionMessageTimeout: null
    submitActionTab: null
    submitActionUrl: null
    submitMethod: ajax
    validationOnFocus: false
    validationOnSubmit: true
  userDeletedAction: retain
defaultStatus: a2dbf537-3f70-40e5-abec-94f2f9d7f936 # New
handle: whitepaperForm
name: 'Whitepaper form'
submitActionEntry: null
template: null
