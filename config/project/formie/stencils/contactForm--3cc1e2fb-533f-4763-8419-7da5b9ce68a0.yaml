data:
  dataRetention: forever
  dataRetentionValue: null
  fileUploadsAction: retain
  notifications:
    -
      attachAssets: null
      attachFiles: true
      attachPdf: null
      bcc: null
      cc: null
      conditions:
        conditionRule: all
        sendRule: send
      content: '[{"type":"paragraph","content":[{"type":"text","text":"A user submission has been made on the \""},{"type":"variableTag","attrs":{"label":"Form Name","value":"{formName}"}},{"type":"text","text":"\" form on "},{"type":"variableTag","attrs":{"label":"Site Name","value":"{siteName}"}},{"type":"text","text":" at "},{"type":"variableTag","attrs":{"label":"Timestamp (yyyy-mm-dd hh:mm:ss)","value":"{timestamp}"}}]},{"type":"paragraph","content":[{"type":"text","text":"Their submission details are:"}]},{"type":"paragraph","content":[{"type":"variableTag","attrs":{"label":"All Form Fields","value":"{allFields}"}}]}]'
      enableConditions: false
      enabled: true
      from: '{field.emailAddress}'
      fromName: null
      handle: adminNotification
      name: 'Admin Notification'
      pdfTemplateId: null
      recipients: email
      replyTo: '{field.emailAddress}'
      replyToName: null
      sender: null
      subject: 'A new submission was made on "{formName}"'
      templateId: null
      to: '{systemEmail}'
      toConditions: null
    -
      attachAssets: null
      attachFiles: true
      attachPdf: null
      bcc: null
      cc: null
      conditions:
        conditionRule: all
        sendRule: send
      content: '[{"type":"paragraph","content":[{"type":"text","text":"Thanks again for contacting us. Our team will get back to you as soon as we can."}]},{"type":"paragraph","content":[{"type":"text","text":"As a reminder, you submitted the following details at "},{"type":"variableTag","attrs":{"label":"Timestamp (yyyy-mm-dd hh:mm:ss)","value":"{timestamp}"}}]},{"type":"paragraph","content":[{"type":"variableTag","attrs":{"label":"All Form Fields","value":"{allFields}"}}]}]'
      enableConditions: false
      enabled: true
      from: null
      fromName: null
      handle: userNotification
      name: 'User Notification'
      pdfTemplateId: null
      recipients: email
      replyTo: null
      replyToName: null
      sender: null
      subject: 'Thanks for contacting us!'
      templateId: null
      to: '{field.emailAddress}'
      toConditions: null
  pages:
    -
      label: 'Page 1'
      rows:
        -
          fields:
            -
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: howCanWeConnectWithYou
                headingSize: h2
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                label: 'How can we connect with you?'
                labelPosition: null
                matchField: null
                placeholder: null
                prePopulate: null
                required: false
                visibility: null
              type: verbb\formie\fields\Heading
        -
          fields:
            -
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: talkTheTalk
                headingSize: h6
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                label: 'TALK THE TALK'
                labelPosition: null
                matchField: null
                placeholder: null
                prePopulate: null
                required: false
                visibility: null
              type: verbb\formie\fields\Heading
        -
          fields:
            -
              settings:
                conditions: null
                containerAttributes: null
                contentTable: null
                cssClasses: null
                defaultValue: null
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: name1
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: verbb\formie\positions\AboveInput
                label: Name
                labelPosition: null
                matchField: null
                nestedLayoutId: null
                placeholder: null
                prePopulate: null
                required: true
                rows:
                  -
                    fields:
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: false
                          errorMessage: null
                          handle: prefix
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: honorific-prefix
                          instructions: null
                          instructionsPosition: null
                          label: Prefix
                          labelPosition: null
                          layout: null
                          limitOptions: false
                          matchField: null
                          max: null
                          min: null
                          multi: false
                          optgroups: true
                          options:
                            -
                              label: 'Select an option'
                              value: ''
                            -
                              label: Mr.
                              value: mr
                            -
                              label: Mrs.
                              value: mrs
                            -
                              label: Ms.
                              value: ms
                            -
                              label: Miss.
                              value: miss
                            -
                              label: Mx.
                              value: mx
                            -
                              label: Dr.
                              value: dr
                            -
                              label: Prof.
                              value: prof
                          placeholder: null
                          prePopulate: null
                          required: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NamePrefix
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: true
                          errorMessage: null
                          handle: firstName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: given-name
                          instructions: null
                          instructionsPosition: null
                          label: 'First Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameFirst
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: false
                          errorMessage: null
                          handle: middleName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: additional-name
                          instructions: null
                          instructionsPosition: null
                          label: 'Middle Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameMiddle
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: true
                          errorMessage: null
                          handle: lastName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: family-name
                          instructions: null
                          instructionsPosition: null
                          label: 'Last Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameLast
                subFieldLabelPosition: null
                useMultipleFields: false
                visibility: null
              type: verbb\formie\fields\Name
        -
          fields:
            -
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: emailAddress
                includeInEmail: true
                inputAttributes: null
                instructions: 'Please enter your email so we can get in touch.'
                instructionsPosition: null
                label: 'Business e-mailaddress'
                labelPosition: null
                matchField: null
                placeholder: 'e.g. <EMAIL>'
                prePopulate: null
                required: true
                uniqueValue: false
                validateDomain: false
                visibility: null
              type: verbb\formie\fields\Email
        -
          fields:
            -
              settings:
                conditions: null
                containerAttributes: null
                contentTable: null
                cssClasses: null
                defaultValue: null
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: company
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: verbb\formie\positions\AboveInput
                label: 'Company name'
                labelPosition: null
                matchField: null
                nestedLayoutId: null
                placeholder: null
                prePopulate: null
                required: true
                rows:
                  -
                    fields:
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: false
                          errorMessage: null
                          handle: prefix
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: honorific-prefix
                          instructions: null
                          instructionsPosition: null
                          label: Prefix
                          labelPosition: null
                          layout: null
                          limitOptions: false
                          matchField: null
                          max: null
                          min: null
                          multi: false
                          optgroups: true
                          options:
                            -
                              label: 'Select an option'
                              value: ''
                            -
                              label: Mr.
                              value: mr
                            -
                              label: Mrs.
                              value: mrs
                            -
                              label: Ms.
                              value: ms
                            -
                              label: Miss.
                              value: miss
                            -
                              label: Mx.
                              value: mx
                            -
                              label: Dr.
                              value: dr
                            -
                              label: Prof.
                              value: prof
                          placeholder: null
                          prePopulate: null
                          required: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NamePrefix
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: true
                          errorMessage: null
                          handle: firstName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: given-name
                          instructions: null
                          instructionsPosition: null
                          label: 'First Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameFirst
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: false
                          errorMessage: null
                          handle: middleName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: additional-name
                          instructions: null
                          instructionsPosition: null
                          label: 'Middle Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameMiddle
                      -
                        settings:
                          conditions: null
                          containerAttributes: null
                          cssClasses: null
                          defaultValue: null
                          emailValue: null
                          enableConditions: false
                          enableContentEncryption: false
                          enabled: true
                          errorMessage: null
                          handle: lastName
                          includeInEmail: true
                          inputAttributes:
                            -
                              label: autocomplete
                              value: family-name
                          instructions: null
                          instructionsPosition: null
                          label: 'Last Name'
                          labelPosition: null
                          limit: false
                          matchField: null
                          max: null
                          maxType: characters
                          min: null
                          minType: characters
                          placeholder: null
                          prePopulate: null
                          required: false
                          uniqueValue: false
                          visibility: null
                        type: verbb\formie\fields\subfields\NameLast
                subFieldLabelPosition: null
                useMultipleFields: false
                visibility: null
              type: verbb\formie\fields\Name
        -
          fields:
            -
              settings:
                conditions:
                  conditionRule: all
                  showRule: show
                containerAttributes: null
                cssClasses: null
                defaultValue: 'Please enter your comments.'
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: message
                includeInEmail: true
                inputAttributes: null
                instructions: 'Please enter your comments.'
                instructionsPosition: null
                label: 'What is on your mind'
                labelPosition: null
                limit: false
                matchField: null
                max: null
                maxType: characters
                min: null
                minType: characters
                placeholder: 'What is on your mind'
                prePopulate: null
                required: true
                richTextButtons:
                  - bold
                  - italic
                uniqueValue: false
                useRichText: false
                visibility: null
              type: verbb\formie\fields\MultiLineText
        -
          fields:
            -
              settings:
                checkedValue: '1'
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: false
                description:
                  -
                    attrs:
                      textAlign: start
                    content:
                      -
                        text: 'Ik ga akkoord met de '
                        type: text
                      -
                        marks:
                          -
                            attrs:
                              class: null
                              href: 'https://redkiwi.com.local/all-content-blocks#entry:113@1'
                              rel: 'noopener noreferrer nofollow'
                              target: ''
                            type: link
                        text: voorwaarden
                        type: text
                    type: paragraph
                emailValue: null
                enableConditions: false
                enableContentEncryption: false
                enabled: true
                errorMessage: null
                handle: agreement
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                label: Agreement
                labelPosition: verbb\formie\positions\Hidden
                matchField: null
                placeholder: null
                prePopulate: null
                required: true
                uncheckedValue: '0'
                visibility: null
              type: verbb\formie\fields\Agree
      settings:
        backButtonLabel: Back
        buttonsPosition: left
        containerAttributes: null
        cssClasses: null
        enableJsEvents: false
        enableNextButtonConditions: false
        enablePageConditions: false
        inputAttributes: null
        saveButtonLabel: Save
        saveButtonStyle: link
        showBackButton: false
        showSaveButton: false
        submitButtonLabel: 'Contact us'
  settings:
    collectIp: false
    collectUser: false
    dataRetention: null
    dataRetentionValue: null
    defaultEmailTemplateId: null
    defaultInstructionsPosition: verbb\formie\positions\AboveInput
    defaultLabelPosition: verbb\formie\positions\AboveInput
    disableCaptchas: false
    displayCurrentPageTitle: false
    displayFormTitle: false
    displayPageProgress: false
    displayPageTabs: false
    errorMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Couldn’t save submission due to errors."}]}]'
    errorMessagePosition: top-form
    fileUploadsAction: null
    integrations:
      activeCampaign:
        accountFieldMapping:
          1: ''
          2: ''
          3: ''
          4: ''
          5: ''
          6: ''
          7: ''
          8: ''
          9: ''
          10: ''
          11: ''
          12: ''
          name: ''
        contactFieldMapping:
          1: ''
          2: ''
          8: ''
          9: ''
          10: ''
          11: ''
          12: ''
          13: ''
          15: ''
          16: ''
          19: ''
          20: ''
          21: ''
          email: ''
          firstName: ''
          lastName: ''
          listId: ''
          phone: ''
          tags: ''
        dealFieldMapping:
          1: ''
          2: ''
          currency: ''
          description: ''
          group: ''
          owner: ''
          percent: ''
          stage: ''
          status: ''
          title: ''
          value: ''
        enabled: '1'
        mapToAccount: ''
        mapToContact: ''
        mapToDeal: ''
        optInField: ''
      recaptcha:
        enabled: '1'
        showAllPages: ''
    limitSubmissions: false
    limitSubmissionsMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    limitSubmissionsNumber: null
    limitSubmissionsType: total
    loadingIndicator: spinner
    loadingIndicatorText: null
    pageRedirectUrl: null
    progressPosition: end
    redirectUrl: null
    requireUser: false
    requireUserMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    requiredIndicator: asterisk
    scheduleForm: false
    scheduleFormEnd: null
    scheduleFormExpiredMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleFormPendingMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleFormStart: null
    scrollToTop: true
    submissionTitleFormat: '{timestamp}'
    submitAction: message
    submitActionFormHide: true
    submitActionMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Thank you for contacting us! Our team will get in touch shortly to follow up on your message."}]}]'
    submitActionMessagePosition: top-form
    submitActionMessageTimeout: null
    submitActionTab: null
    submitActionUrl: null
    submitMethod: ajax
    validationOnFocus: true
    validationOnSubmit: true
  userDeletedAction: retain
defaultStatus: a2dbf537-3f70-40e5-abec-94f2f9d7f936 # New
handle: contactForm
name: 'Contact Form'
submitActionEntry: null
template: null
