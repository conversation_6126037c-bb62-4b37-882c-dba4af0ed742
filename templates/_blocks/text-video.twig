{% set fallbackImage = block.fallbackImage.one() | default() %}

{% extends '_layouts/block' %}

{% set blockClass = 'text-video animation--fade-in container' %}
{% if not block.marginBottom %}
    {% set lastBlockMarginClass = 'lg:!mb-0' %}
{% endif %}
{% set linkItem = block.linkItem.one() %}

{% block blockContent %}
    <div class="flex flex-col justify-between items-center {{ block.textVideoLayout == 'text-video' ? 'lg:flex-row-reverse' : 'lg:flex-row' }}">
        <div class="
            relative w-full
            lg:w-1/2
        ">
            {% if block.videoSrc %}
                <video
                    muted
                    loop
                    autoplay
                    {% if fallbackImage %}
                        poster="{{ fallbackImage.getUrl({
                            width: 1440,
                            format: 'webp'
                        }) }}"
                    {% endif %}
                    controls
                    class="object-cover size-full aspect-video"
                >
                    <source src="{{ block.videoSrc }}" type="video/mp4" />
                </video>
            {% endif %}
        </div>
        <div class="w-full lg:w-5/12">
            {% if block.wysiwyg %}
                <div class="prose wysiwyg mb-4">
                    {{ block.wysiwyg }}
                </div>
            {% endif %}
            {% if linkItem and (linkItem.linkNode is not empty and linkItem.linkNode.url is not empty) %}
                {% set link = linkItem.linkNode %}
                {% include '_parts/button' with {
                    link,
                    label: link.getText(),
                    labelHover: linkItem.hoverText,
                } %}
            {% endif %}
        </div>
    </div>
{% endblock %}
